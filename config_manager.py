"""
智能睡眠音频评估系统 - 配置管理器
支持YAML配置文件加载、验证、热更新功能
"""

import os
import json
import time
import threading
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
import copy

# 尝试导入yaml，如果失败则使用json作为备选
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("警告: PyYAML未安装，将使用JSON格式作为配置文件格式")

# 尝试导入watchdog，如果失败则禁用热更新功能
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    print("警告: watchdog未安装，热更新功能将被禁用")

# 配置验证异常
class ConfigValidationError(Exception):
    """配置验证错误"""
    pass

class ConfigLoadError(Exception):
    """配置加载错误"""
    pass

@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    file_path: str
    change_type: str  # 'modified', 'created', 'deleted'
    timestamp: float
    old_config: Optional[Dict[str, Any]] = None
    new_config: Optional[Dict[str, Any]] = None

if WATCHDOG_AVAILABLE:
    class ConfigFileHandler(FileSystemEventHandler):
        """配置文件监控处理器"""

        def __init__(self, config_manager):
            self.config_manager = config_manager
            self.last_modified = {}

        def on_modified(self, event):
            if event.is_directory:
                return

            file_path = event.src_path
            if not file_path.endswith(('.yaml', '.yml', '.json')):
                return

            # 防止重复触发
            current_time = time.time()
            if file_path in self.last_modified:
                if current_time - self.last_modified[file_path] < 1.0:
                    return
            self.last_modified[file_path] = current_time

            # 延迟重载，避免文件写入过程中读取
            threading.Timer(
                self.config_manager.reload_delay,
                self.config_manager._reload_config_file,
                args=[file_path]
            ).start()
else:
    # 如果watchdog不可用，创建一个空的处理器类
    class ConfigFileHandler:
        def __init__(self, config_manager):
            pass

class ConfigManager:
    """配置管理器主类"""

    def __init__(self, config_dir: str = "config", default_config: str = None):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录
            default_config: 默认配置文件名
        """
        self.config_dir = Path(config_dir)

        # 根据可用的库选择配置文件格式
        if default_config is None:
            if YAML_AVAILABLE:
                default_config = "default.yaml"
            else:
                default_config = "default.json"

        self.default_config_path = self.config_dir / default_config
        self.config = {}
        self.config_history = []
        self.change_callbacks = []
        self.reload_delay = 1.0
        self.hot_reload_enabled = False
        self.observer = None
        self.lock = threading.RLock()

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)

        # 加载配置
        self.load_config()

        # 启动热更新监控
        if WATCHDOG_AVAILABLE and self.get('hot_reload.enabled', False):
            self.enable_hot_reload()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        with self.lock:
            try:
                # 加载默认配置
                if self.default_config_path.exists():
                    with open(self.default_config_path, 'r', encoding='utf-8') as f:
                        if self.default_config_path.suffix.lower() in ['.yaml', '.yml']:
                            if YAML_AVAILABLE:
                                self.config = yaml.safe_load(f) or {}
                            else:
                                raise ConfigLoadError("YAML文件需要PyYAML库支持")
                        else:
                            self.config = json.load(f) or {}
                else:
                    self.logger.warning(f"默认配置文件不存在: {self.default_config_path}")
                    self.config = {}

                # 加载其他配置文件并合并
                config_patterns = ["*.yaml", "*.yml"] if YAML_AVAILABLE else []
                config_patterns.append("*.json")

                for pattern in config_patterns:
                    for config_file in self.config_dir.glob(pattern):
                        if config_file != self.default_config_path:
                            try:
                                with open(config_file, 'r', encoding='utf-8') as f:
                                    if config_file.suffix.lower() in ['.yaml', '.yml']:
                                        if YAML_AVAILABLE:
                                            additional_config = yaml.safe_load(f) or {}
                                        else:
                                            continue  # 跳过YAML文件
                                    else:
                                        additional_config = json.load(f) or {}

                                    self.config = self._merge_configs(self.config, additional_config)
                            except Exception as e:
                                self.logger.error(f"加载配置文件失败 {config_file}: {e}")

                # 验证配置
                self._validate_config()

                # 更新热更新设置
                self.reload_delay = self.get('hot_reload.reload_delay_seconds', 1.0)

                self.logger.info("配置加载成功")
                return copy.deepcopy(self.config)

            except Exception as e:
                self.logger.error(f"配置加载失败: {e}")
                raise ConfigLoadError(f"配置加载失败: {e}")
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """递归合并配置字典"""
        result = copy.deepcopy(base)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = copy.deepcopy(value)
        
        return result
    
    def _validate_config(self):
        """验证配置文件的有效性"""
        try:
            # 验证必需的配置项
            required_sections = ['system', 'analysis', 'noise_types', 'safety_thresholds']
            for section in required_sections:
                if section not in self.config:
                    raise ConfigValidationError(f"缺少必需的配置节: {section}")
            
            # 验证系统配置
            system_config = self.config.get('system', {})
            if 'version' not in system_config:
                raise ConfigValidationError("缺少系统版本配置")
            
            # 验证安全阈值
            safety_config = self.config.get('safety_thresholds', {})
            required_groups = ['adult', 'infant', 'elderly', 'insomnia']
            for group in required_groups:
                if group not in safety_config:
                    raise ConfigValidationError(f"缺少用户群体安全配置: {group}")
                
                group_config = safety_config[group]
                required_fields = ['max_db', 'min_distance_cm', 'max_duration_hours']
                for field in required_fields:
                    if field not in group_config:
                        raise ConfigValidationError(f"用户群体 {group} 缺少配置项: {field}")
            
            # 验证噪音类型配置
            noise_types = self.config.get('noise_types', {})
            for noise_type, config in noise_types.items():
                if 'effectiveness' not in config:
                    raise ConfigValidationError(f"噪音类型 {noise_type} 缺少有效性配置")
                
                effectiveness = config['effectiveness']
                if not (0 <= effectiveness <= 1):
                    raise ConfigValidationError(f"噪音类型 {noise_type} 有效性值无效: {effectiveness}")
            
            # 验证数值范围
            validation_config = self.config.get('validation', {})
            if 'parameters' in validation_config:
                params = validation_config['parameters']
                
                # 验证音量范围
                if 'volume_range' in params:
                    vol_range = params['volume_range']
                    if len(vol_range) != 2 or vol_range[0] >= vol_range[1]:
                        raise ConfigValidationError("音量范围配置无效")
                
                # 验证距离范围
                if 'distance_range' in params:
                    dist_range = params['distance_range']
                    if len(dist_range) != 2 or dist_range[0] >= dist_range[1]:
                        raise ConfigValidationError("距离范围配置无效")
            
            self.logger.info("配置验证通过")
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            raise ConfigValidationError(f"配置验证失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'section.subsection.key' 格式
            default: 默认值
            
        Returns:
            配置值
        """
        with self.lock:
            keys = key.split('.')
            value = self.config
            
            try:
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                return default
    
    def set(self, key: str, value: Any, save: bool = False):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            save: 是否保存到文件
        """
        with self.lock:
            keys = key.split('.')
            config = self.config
            
            # 导航到目标位置
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            old_value = config.get(keys[-1])
            config[keys[-1]] = value
            
            # 触发变更回调
            self._trigger_change_callbacks(key, old_value, value)
            
            # 保存到文件
            if save:
                self.save_config()
    
    def save_config(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        with self.lock:
            if file_path is None:
                file_path = self.default_config_path
            else:
                file_path = Path(file_path)

            try:
                # 备份现有配置
                if self.get('hot_reload.backup_on_reload', True) and file_path.exists():
                    backup_suffix = f'.backup.{int(time.time())}'
                    if file_path.suffix.lower() in ['.yaml', '.yml']:
                        backup_path = file_path.with_suffix(backup_suffix + '.yaml')
                    else:
                        backup_path = file_path.with_suffix(backup_suffix + '.json')
                    file_path.rename(backup_path)

                # 保存新配置
                with open(file_path, 'w', encoding='utf-8') as f:
                    if file_path.suffix.lower() in ['.yaml', '.yml']:
                        if YAML_AVAILABLE:
                            yaml.dump(self.config, f, default_flow_style=False,
                                     allow_unicode=True, indent=2)
                        else:
                            raise ConfigLoadError("保存YAML文件需要PyYAML库支持")
                    else:
                        json.dump(self.config, f, ensure_ascii=False, indent=2)

                self.logger.info(f"配置已保存到: {file_path}")

            except Exception as e:
                self.logger.error(f"保存配置失败: {e}")
                raise
    
    def enable_hot_reload(self):
        """启用配置热更新"""
        if not WATCHDOG_AVAILABLE:
            self.logger.warning("watchdog库不可用，无法启用热更新功能")
            return

        if self.hot_reload_enabled:
            return

        try:
            self.observer = Observer()
            handler = ConfigFileHandler(self)

            # 监控配置目录
            self.observer.schedule(handler, str(self.config_dir), recursive=False)

            # 监控模板目录（如果存在）
            template_dir = Path("templates")
            if template_dir.exists():
                self.observer.schedule(handler, str(template_dir), recursive=True)

            self.observer.start()
            self.hot_reload_enabled = True
            self.logger.info("配置热更新已启用")

        except Exception as e:
            self.logger.error(f"启用热更新失败: {e}")
    
    def disable_hot_reload(self):
        """禁用配置热更新"""
        if not self.hot_reload_enabled:
            return
        
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join()
                self.observer = None
            
            self.hot_reload_enabled = False
            self.logger.info("配置热更新已禁用")
            
        except Exception as e:
            self.logger.error(f"禁用热更新失败: {e}")
    
    def _reload_config_file(self, file_path: str):
        """重新加载指定的配置文件"""
        try:
            old_config = copy.deepcopy(self.config)
            self.load_config()
            
            # 创建变更事件
            event = ConfigChangeEvent(
                file_path=file_path,
                change_type='modified',
                timestamp=time.time(),
                old_config=old_config,
                new_config=copy.deepcopy(self.config)
            )
            
            # 触发变更回调
            for callback in self.change_callbacks:
                try:
                    callback(event)
                except Exception as e:
                    self.logger.error(f"配置变更回调执行失败: {e}")
            
            self.logger.info(f"配置文件已重新加载: {file_path}")
            
        except Exception as e:
            self.logger.error(f"重新加载配置文件失败 {file_path}: {e}")
    
    def add_change_callback(self, callback: Callable[[ConfigChangeEvent], None]):
        """添加配置变更回调函数"""
        self.change_callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable[[ConfigChangeEvent], None]):
        """移除配置变更回调函数"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
    
    def _trigger_change_callbacks(self, key: str, old_value: Any, new_value: Any):
        """触发配置变更回调"""
        event = ConfigChangeEvent(
            file_path="runtime",
            change_type="modified",
            timestamp=time.time(),
            old_config={key: old_value},
            new_config={key: new_value}
        )
        
        for callback in self.change_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"配置变更回调执行失败: {e}")
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取完整配置的副本"""
        with self.lock:
            return copy.deepcopy(self.config)
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        with self.lock:
            if self.default_config_path.exists():
                with open(self.default_config_path, 'r', encoding='utf-8') as f:
                    if self.default_config_path.suffix.lower() in ['.yaml', '.yml']:
                        if YAML_AVAILABLE:
                            self.config = yaml.safe_load(f) or {}
                        else:
                            raise ConfigLoadError("YAML文件需要PyYAML库支持")
                    else:
                        self.config = json.load(f) or {}
                self._validate_config()
                self.logger.info("配置已重置为默认值")
            else:
                raise ConfigLoadError("默认配置文件不存在")
    
    def __del__(self):
        """析构函数，清理资源"""
        self.disable_hot_reload()

# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_config(key: str, default: Any = None) -> Any:
    """便捷函数：获取配置值"""
    return get_config_manager().get(key, default)

def set_config(key: str, value: Any, save: bool = False):
    """便捷函数：设置配置值"""
    get_config_manager().set(key, value, save)
