#!/usr/bin/env python3
"""
配置系统单元测试
"""

import unittest
import tempfile
import json
import os
from pathlib import Path
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager, ConfigValidationError, ConfigLoadError
from template_engine import TemplateManager, TemplateError

class TestConfigManager(unittest.TestCase):
    """配置管理器单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        # 创建测试配置文件
        self.test_config = {
            "system": {
                "version": "2.0",
                "debug_mode": False
            },
            "analysis": {
                "audio_processing": {
                    "default_sample_rate": None
                }
            },
            "noise_types": {
                "pink": {
                    "effectiveness": 0.82,
                    "spectral_slope_range": [-1.2, -0.8]
                }
            },
            "safety_thresholds": {
                "adult": {
                    "max_db": 60,
                    "min_distance_cm": 30
                }
            }
        }
        
        config_file = self.config_dir / "test.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f, indent=2)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_config_loading(self):
        """测试配置加载"""
        config_manager = ConfigManager(str(self.config_dir), "test.json")
        
        # 测试基本配置获取
        version = config_manager.get('system.version')
        self.assertEqual(version, "2.0")
        
        # 测试嵌套配置获取
        max_db = config_manager.get('safety_thresholds.adult.max_db')
        self.assertEqual(max_db, 60)
        
        # 测试默认值
        non_existent = config_manager.get('non.existent.key', 'default')
        self.assertEqual(non_existent, 'default')
    
    def test_config_setting(self):
        """测试配置设置"""
        config_manager = ConfigManager(str(self.config_dir), "test.json")
        
        # 设置新值
        config_manager.set('test.new_key', 'new_value')
        self.assertEqual(config_manager.get('test.new_key'), 'new_value')
        
        # 设置嵌套值
        config_manager.set('nested.deep.value', 42)
        self.assertEqual(config_manager.get('nested.deep.value'), 42)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 创建无效配置
        invalid_config = {"invalid": "config"}
        invalid_file = self.config_dir / "invalid.json"
        with open(invalid_file, 'w', encoding='utf-8') as f:
            json.dump(invalid_config, f)
        
        # 应该抛出验证错误
        with self.assertRaises(ConfigValidationError):
            ConfigManager(str(self.config_dir), "invalid.json")
    
    def test_config_merge(self):
        """测试配置合并"""
        # 创建额外配置文件
        extra_config = {
            "system": {
                "debug_mode": True,  # 覆盖原值
                "new_setting": "test"  # 新增设置
            },
            "new_section": {
                "value": 123
            }
        }
        
        extra_file = self.config_dir / "extra.json"
        with open(extra_file, 'w', encoding='utf-8') as f:
            json.dump(extra_config, f)
        
        config_manager = ConfigManager(str(self.config_dir), "test.json")
        
        # 检查合并结果
        self.assertTrue(config_manager.get('system.debug_mode'))  # 被覆盖
        self.assertEqual(config_manager.get('system.new_setting'), "test")  # 新增
        self.assertEqual(config_manager.get('new_section.value'), 123)  # 新节
        self.assertEqual(config_manager.get('system.version'), "2.0")  # 保持原值

class TestTemplateManager(unittest.TestCase):
    """模板管理器单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.template_dir = Path(self.temp_dir) / "templates"
        self.template_dir.mkdir(exist_ok=True)
        
        # 创建测试模板
        self.test_template = """
Hello {{ name }}!
Your score is {{ score }}.
{% if score > 80 %}
Excellent work!
{% endif %}
"""
        
        template_file = self.template_dir / "test.txt"
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(self.test_template)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_template_rendering(self):
        """测试模板渲染"""
        template_manager = TemplateManager(str(self.template_dir))
        
        context = {
            'name': 'Alice',
            'score': 85
        }
        
        result = template_manager.render('test.txt', context)
        
        # 检查渲染结果
        self.assertIn('Hello Alice!', result)
        self.assertIn('Your score is 85', result)
        self.assertIn('Excellent work!', result)
    
    def test_template_conditionals(self):
        """测试模板条件语句"""
        template_manager = TemplateManager(str(self.template_dir))
        
        # 低分情况
        context_low = {
            'name': 'Bob',
            'score': 60
        }
        
        result_low = template_manager.render('test.txt', context_low)
        self.assertNotIn('Excellent work!', result_low)
        
        # 高分情况
        context_high = {
            'name': 'Charlie',
            'score': 90
        }
        
        result_high = template_manager.render('test.txt', context_high)
        self.assertIn('Excellent work!', result_high)
    
    def test_template_creation(self):
        """测试模板创建"""
        template_manager = TemplateManager(str(self.template_dir))
        
        new_template = "Welcome {{ user }}!"
        template_manager.create_template('welcome.txt', new_template)
        
        # 验证模板文件存在
        self.assertTrue(template_manager.template_exists('welcome.txt'))
        
        # 验证模板渲染
        result = template_manager.render('welcome.txt', {'user': 'David'})
        self.assertEqual(result.strip(), 'Welcome David!')
    
    def test_template_listing(self):
        """测试模板列表"""
        template_manager = TemplateManager(str(self.template_dir))
        
        templates = template_manager.list_templates()
        self.assertIn('test.txt', templates)

class TestConfigDrivenIntegration(unittest.TestCase):
    """配置驱动系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建配置目录和文件
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        self.test_config = {
            "system": {"version": "2.0"},
            "analysis": {
                "audio_processing": {
                    "default_sample_rate": None
                }
            },
            "noise_types": {
                "pink": {"effectiveness": 0.82},
                "white": {"effectiveness": 0.33}
            },
            "safety_thresholds": {
                "adult": {"max_db": 60, "min_distance_cm": 30}
            },
            "sleep_evaluation": {
                "scoring_weights": {
                    "stability_factor": 0.30,
                    "tonal_factor": 0.30
                }
            }
        }
        
        config_file = self.config_dir / "test.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_config, f, indent=2)
        
        # 创建模板目录和文件
        self.template_dir = Path(self.temp_dir) / "templates"
        self.template_dir.mkdir(exist_ok=True)
        
        self.test_template = """
Analysis Report
===============
File: {{ filename }}
Score: {{ score }}
Type: {{ noise_type }}
"""
        
        template_file = self.template_dir / "report.txt"
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(self.test_template)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_config_template_integration(self):
        """测试配置和模板的集成"""
        # 初始化管理器
        config_manager = ConfigManager(str(self.config_dir), "test.json")
        template_manager = TemplateManager(str(self.template_dir))
        
        # 从配置获取数据
        pink_effectiveness = config_manager.get('noise_types.pink.effectiveness')
        adult_max_db = config_manager.get('safety_thresholds.adult.max_db')
        
        # 使用模板生成报告
        context = {
            'filename': 'test.wav',
            'score': 85,
            'noise_type': 'pink',
            'effectiveness': pink_effectiveness,
            'max_db': adult_max_db
        }
        
        report = template_manager.render('report.txt', context)
        
        # 验证结果
        self.assertIn('test.wav', report)
        self.assertIn('85', report)
        self.assertIn('pink', report)
    
    def test_hot_reload_simulation(self):
        """测试热更新模拟"""
        config_manager = ConfigManager(str(self.config_dir), "test.json")
        
        # 获取初始值
        initial_value = config_manager.get('noise_types.pink.effectiveness')
        self.assertEqual(initial_value, 0.82)
        
        # 修改配置文件
        modified_config = self.test_config.copy()
        modified_config['noise_types']['pink']['effectiveness'] = 0.90
        
        config_file = self.config_dir / "test.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(modified_config, f, indent=2)
        
        # 重新加载配置
        config_manager.load_config()
        
        # 验证更新
        updated_value = config_manager.get('noise_types.pink.effectiveness')
        self.assertEqual(updated_value, 0.90)

def run_unit_tests():
    """运行所有单元测试"""
    print("🧪 运行配置系统单元测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestConfigManager,
        TestTemplateManager,
        TestConfigDrivenIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_unit_tests()
    if success:
        print("\n✅ 所有单元测试通过！")
    else:
        print("\n❌ 部分单元测试失败！")
        sys.exit(1)
