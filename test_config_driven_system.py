#!/usr/bin/env python3
"""
测试配置驱动的智能睡眠音频评估系统
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_driven_sleep_system import ConfigDrivenSleepAudioSystem, create_config_driven_system, analyze_with_config

def test_config_driven_system():
    """测试配置驱动系统基本功能"""
    print("🧪 测试配置驱动的睡眠音频评估系统...")
    
    try:
        # 测试系统初始化
        print("1. 测试系统初始化...")
        system = ConfigDrivenSleepAudioSystem()
        print("   ✅ 配置驱动系统初始化成功")
        
        # 检查配置加载
        print("2. 检查配置加载...")
        print(f"   噪音类型有效性配置: {len(system.noise_effectiveness)} 项")
        print(f"   安全阈值配置: {len(system.safety_thresholds)} 个用户群体")
        print(f"   评分权重配置: {system.scoring_weights}")
        print("   ✅ 配置加载验证通过")
        
        # 测试便捷函数
        print("3. 测试便捷函数...")
        system2 = create_config_driven_system()
        print("   ✅ 便捷函数创建系统成功")
        
        print("\n✅ 配置驱动系统基础测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_analysis():
    """测试音频分析功能"""
    print("\n🧪 测试音频分析功能...")
    
    # 查找测试音频文件
    test_files = []
    noisekun_dir = Path("noisekun")
    if noisekun_dir.exists():
        test_files = list(noisekun_dir.glob("*.ogm"))[:3]  # 只测试前3个文件
    
    if not test_files:
        print("   ⚠️ 未找到测试音频文件，跳过音频分析测试")
        return True
    
    try:
        system = ConfigDrivenSleepAudioSystem()
        
        for i, audio_file in enumerate(test_files, 1):
            print(f"   {i}. 分析文件: {audio_file.name}")
            
            # 分析音频文件
            report = system.analyze_audio_file(str(audio_file))
            
            print(f"      📊 睡眠适用性: {report.sleep_suitability.overall_score:.1f}/100")
            print(f"      🛡️ 安全等级: {report.safety_assessment.overall_safety.value}")
            print(f"      🎵 噪音类型: {report.audio_features.noise_type.value}")
            print(f"      💡 总体推荐: {report.overall_recommendation}")
            
        print("   ✅ 音频分析测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 音频分析测试失败: {e}")
        return False

def test_report_generation():
    """测试报告生成功能"""
    print("\n🧪 测试报告生成功能...")
    
    # 查找一个测试音频文件
    test_file = None
    noisekun_dir = Path("noisekun")
    if noisekun_dir.exists():
        test_files = list(noisekun_dir.glob("*.ogm"))
        if test_files:
            test_file = test_files[0]
    
    if not test_file:
        print("   ⚠️ 未找到测试音频文件，跳过报告生成测试")
        return True
    
    try:
        system = ConfigDrivenSleepAudioSystem()
        
        # 分析音频文件
        print(f"   分析文件: {test_file.name}")
        report = system.analyze_audio_file(str(test_file))
        
        # 测试不同格式的报告生成
        formats = ['text', 'markdown', 'json']
        
        for format_type in formats:
            print(f"   生成 {format_type} 格式报告...")
            try:
                generated_report = system.generate_report(report, format_type)
                print(f"      报告长度: {len(generated_report)} 字符")
                
                # 保存测试报告
                output_file = f"test_config_driven_report.{format_type}"
                if format_type == 'text':
                    output_file = f"test_config_driven_report.txt"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(generated_report)
                print(f"      已保存: {output_file}")
                
            except Exception as e:
                print(f"      ⚠️ {format_type} 格式生成失败: {e}")
        
        # 测试用户群体特定报告
        print("   生成用户群体特定报告...")
        for user_group in ['adult', 'infant', 'elderly', 'insomnia']:
            try:
                group_report = system.generate_report(report, 'text', user_group)
                print(f"      {user_group} 群体报告长度: {len(group_report)} 字符")
            except Exception as e:
                print(f"      ⚠️ {user_group} 群体报告生成失败: {e}")
        
        print("   ✅ 报告生成测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 报告生成测试失败: {e}")
        return False

def test_convenience_functions():
    """测试便捷函数"""
    print("\n🧪 测试便捷函数...")
    
    # 查找一个测试音频文件
    test_file = None
    noisekun_dir = Path("noisekun")
    if noisekun_dir.exists():
        test_files = list(noisekun_dir.glob("*.ogm"))
        if test_files:
            test_file = test_files[0]
    
    if not test_file:
        print("   ⚠️ 未找到测试音频文件，跳过便捷函数测试")
        return True
    
    try:
        print(f"   使用便捷函数分析: {test_file.name}")
        
        # 测试便捷分析函数
        result = analyze_with_config(str(test_file), 'text', 'adult')
        print(f"   便捷函数返回报告长度: {len(result)} 字符")
        
        # 保存便捷函数生成的报告
        with open('test_convenience_report.txt', 'w', encoding='utf-8') as f:
            f.write(result)
        print("   已保存: test_convenience_report.txt")
        
        print("   ✅ 便捷函数测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 便捷函数测试失败: {e}")
        return False

def test_configuration_impact():
    """测试配置对系统行为的影响"""
    print("\n🧪 测试配置影响...")
    
    try:
        system = ConfigDrivenSleepAudioSystem()
        
        # 检查配置是否正确影响系统行为
        print("   检查噪音类型有效性配置...")
        from smart_sleep_audio_system import NoiseType
        
        # 检查粉噪音有效性
        pink_effectiveness = system.noise_effectiveness.get(NoiseType.PINK, 0)
        print(f"   粉噪音有效性: {pink_effectiveness}")
        assert pink_effectiveness == 0.82, f"粉噪音有效性应为0.82，实际为{pink_effectiveness}"
        
        # 检查评分权重
        print("   检查评分权重配置...")
        stability_weight = system.scoring_weights.get('stability_factor', 0)
        print(f"   稳定性权重: {stability_weight}")
        assert stability_weight == 0.30, f"稳定性权重应为0.30，实际为{stability_weight}"
        
        # 检查安全阈值
        print("   检查安全阈值配置...")
        from smart_sleep_audio_system import UserGroup
        adult_threshold = system.safety_thresholds.get(UserGroup.ADULT, {})
        adult_max_db = adult_threshold.get('max_db', 0)
        print(f"   成人最大音量: {adult_max_db} dB")
        assert adult_max_db == 60, f"成人最大音量应为60dB，实际为{adult_max_db}dB"
        
        print("   ✅ 配置影响测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 配置影响测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始配置驱动系统测试")
    print("=" * 60)
    
    # 运行测试
    success = True
    success &= test_config_driven_system()
    success &= test_audio_analysis()
    success &= test_report_generation()
    success &= test_convenience_functions()
    success &= test_configuration_impact()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！配置驱动系统准备就绪。")
        print("\n📋 系统特性:")
        print("   ✅ 配置驱动的参数管理")
        print("   ✅ 模板化的报告生成")
        print("   ✅ 热更新支持（如果安装了watchdog）")
        print("   ✅ 多格式输出支持")
        print("   ✅ 用户群体个性化推荐")
        print("   ✅ 100% 向后兼容")
    else:
        print("❌ 部分测试失败，请检查系统配置。")
    
    return success

if __name__ == "__main__":
    main()
