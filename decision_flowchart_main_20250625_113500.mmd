graph TD
    A[📋 用户需求分析] --> B{🔍 需求复杂度评估}
    
    B -->|简单需求<br/>单一功能| C{🛠️ 现有工具支持度}
    B -->|中等复杂度<br/>多功能组合| D{⚙️ 参数扩展可行性}
    B -->|高复杂度<br/>新核心逻辑| E{🏗️ 架构影响评估}
    
    C -->|完全支持<br/>≥90%需求| F[✅ 使用现有工具]
    C -->|部分支持<br/>60-89%需求| G{🔧 参数组合充分性}
    C -->|支持不足<br/><60%需求| H[⚠️ 需要功能扩展]
    
    D -->|可通过参数实现| I[🔧 扩展现有工具]
    D -->|需要新逻辑| J{💰 开发成本评估}
    
    E -->|核心逻辑变更| K[🆕 创建专用脚本]
    E -->|可复用核心| L{🔌 插件化可行性}
    
    F --> M[📄 run_sleep_audio_analysis.py]
    G -->|充分| M
    G -->|不充分| I
    H --> I
    I --> N[📄 enhanced_run_sleep_audio_analysis.py]
    J -->|成本低<br/><2人日| I
    J -->|成本高<br/>≥2人日| K
    K --> O[📄 专用分析脚本]
    L -->|可行| P[🔌 插件化扩展]
    L -->|不可行| K
    
    M --> Q[📊 标准分析报告]
    N --> R[📊 增强分析报告]
    O --> S[📊 专业定制报告]
    P --> T[📊 模块化报告系统]
    
    style A fill:#e3f2fd
    style F fill:#e8f5e8
    style I fill:#fff3e0
    style K fill:#ffebee
    style M fill:#e8f5e8
    style N fill:#fff3e0
    style O fill:#ffebee
    style P fill:#f3e5f5
