"""
智能睡眠音频评估系统 - 模板引擎
支持模板渲染、组件复用、模板继承机制
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from string import Template
import logging
from datetime import datetime

# 尝试导入Jinja2，如果失败则使用内置模板引擎
try:
    import jinja2
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False
    print("警告: Jinja2未安装，将使用内置模板引擎")

class TemplateError(Exception):
    """模板错误"""
    pass

class SimpleTemplateEngine:
    """简化的模板引擎，不依赖Jinja2"""
    
    def __init__(self, template_dir: str = "templates"):
        self.template_dir = Path(template_dir)
        self.template_cache = {}
        self.logger = logging.getLogger(__name__)
        
        # 确保模板目录存在
        self.template_dir.mkdir(exist_ok=True)
        
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """渲染模板"""
        try:
            template_content = self._load_template(template_name)
            return self._render_content(template_content, context)
        except Exception as e:
            self.logger.error(f"模板渲染失败 {template_name}: {e}")
            raise TemplateError(f"模板渲染失败: {e}")
    
    def _load_template(self, template_name: str) -> str:
        """加载模板文件"""
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        
        template_path = self.template_dir / template_name
        if not template_path.exists():
            raise TemplateError(f"模板文件不存在: {template_path}")
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 处理模板继承
            content = self._process_inheritance(content)
            
            # 处理包含指令
            content = self._process_includes(content)
            
            self.template_cache[template_name] = content
            return content
            
        except Exception as e:
            raise TemplateError(f"加载模板文件失败: {e}")
    
    def _process_inheritance(self, content: str) -> str:
        """处理模板继承"""
        # 查找 {% extends "base.txt" %} 指令
        extends_pattern = r'{%\s*extends\s+["\']([^"\']+)["\']\s*%}'
        match = re.search(extends_pattern, content)
        
        if match:
            base_template = match.group(1)
            base_content = self._load_template(base_template)
            
            # 移除extends指令
            content = re.sub(extends_pattern, '', content)
            
            # 处理块替换
            content = self._process_blocks(base_content, content)
        
        return content
    
    def _process_blocks(self, base_content: str, child_content: str) -> str:
        """处理模板块替换"""
        # 查找子模板中的块定义
        block_pattern = r'{%\s*block\s+(\w+)\s*%}(.*?){%\s*endblock\s*%}'
        child_blocks = {}
        
        for match in re.finditer(block_pattern, child_content, re.DOTALL):
            block_name = match.group(1)
            block_content = match.group(2).strip()
            child_blocks[block_name] = block_content
        
        # 替换基模板中的块
        def replace_block(match):
            block_name = match.group(1)
            default_content = match.group(2).strip()
            return child_blocks.get(block_name, default_content)
        
        result = re.sub(block_pattern, replace_block, base_content, flags=re.DOTALL)
        return result
    
    def _process_includes(self, content: str) -> str:
        """处理包含指令"""
        include_pattern = r'{%\s*include\s+["\']([^"\']+)["\']\s*%}'
        
        def replace_include(match):
            include_file = match.group(1)
            try:
                return self._load_template(include_file)
            except Exception as e:
                self.logger.warning(f"包含文件失败 {include_file}: {e}")
                return f"<!-- 包含失败: {include_file} -->"
        
        return re.sub(include_pattern, replace_include, content)
    
    def _render_content(self, content: str, context: Dict[str, Any]) -> str:
        """渲染模板内容"""
        # 处理变量替换 {{ variable }}
        var_pattern = r'{{\s*([^}]+)\s*}}'
        
        def replace_var(match):
            var_expr = match.group(1).strip()
            try:
                return str(self._evaluate_expression(var_expr, context))
            except Exception as e:
                self.logger.warning(f"变量求值失败 {var_expr}: {e}")
                return f"{{{{ {var_expr} }}}}"
        
        content = re.sub(var_pattern, replace_var, content)
        
        # 处理条件语句 {% if condition %}
        content = self._process_conditionals(content, context)
        
        # 处理循环语句 {% for item in items %}
        content = self._process_loops(content, context)
        
        return content
    
    def _evaluate_expression(self, expr: str, context: Dict[str, Any]) -> Any:
        """求值表达式"""
        # 简单的点号访问支持
        if '.' in expr:
            parts = expr.split('.')
            value = context
            for part in parts:
                if isinstance(value, dict):
                    value = value.get(part, '')
                else:
                    value = getattr(value, part, '')
            return value
        else:
            return context.get(expr, '')
    
    def _process_conditionals(self, content: str, context: Dict[str, Any]) -> str:
        """处理条件语句"""
        # 简化的if语句处理
        if_pattern = r'{%\s*if\s+([^%]+)\s*%}(.*?){%\s*endif\s*%}'
        
        def replace_if(match):
            condition = match.group(1).strip()
            if_content = match.group(2)
            
            try:
                # 简单的条件求值
                if self._evaluate_condition(condition, context):
                    return if_content
                else:
                    return ''
            except Exception as e:
                self.logger.warning(f"条件求值失败 {condition}: {e}")
                return if_content
        
        return re.sub(if_pattern, replace_if, content, flags=re.DOTALL)
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """求值条件表达式"""
        # 简化的条件求值
        condition = condition.strip()
        
        # 处理简单的存在性检查
        if condition in context:
            value = context[condition]
            return bool(value)
        
        # 处理点号访问
        if '.' in condition:
            value = self._evaluate_expression(condition, context)
            return bool(value)
        
        return False
    
    def _process_loops(self, content: str, context: Dict[str, Any]) -> str:
        """处理循环语句"""
        # 简化的for循环处理
        for_pattern = r'{%\s*for\s+(\w+)\s+in\s+([^%]+)\s*%}(.*?){%\s*endfor\s*%}'
        
        def replace_for(match):
            item_var = match.group(1).strip()
            iterable_expr = match.group(2).strip()
            loop_content = match.group(3)
            
            try:
                iterable = self._evaluate_expression(iterable_expr, context)
                if not iterable:
                    return ''
                
                result = []
                for item in iterable:
                    loop_context = context.copy()
                    loop_context[item_var] = item
                    rendered_item = self._render_content(loop_content, loop_context)
                    result.append(rendered_item)
                
                return ''.join(result)
            except Exception as e:
                self.logger.warning(f"循环处理失败 {iterable_expr}: {e}")
                return loop_content
        
        return re.sub(for_pattern, replace_for, content, flags=re.DOTALL)

class Jinja2TemplateEngine:
    """Jinja2模板引擎包装器"""
    
    def __init__(self, template_dir: str = "templates"):
        if not JINJA2_AVAILABLE:
            raise TemplateError("Jinja2未安装，无法使用Jinja2模板引擎")
        
        self.template_dir = Path(template_dir)
        self.template_dir.mkdir(exist_ok=True)
        
        # 配置Jinja2环境
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.template_dir)),
            autoescape=jinja2.select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 添加自定义过滤器
        self._add_custom_filters()
        
        self.logger = logging.getLogger(__name__)
    
    def _add_custom_filters(self):
        """添加自定义过滤器"""
        def format_number(value, decimals=2):
            """格式化数字"""
            try:
                return f"{float(value):.{decimals}f}"
            except (ValueError, TypeError):
                return str(value)
        
        def format_percentage(value, decimals=1):
            """格式化百分比"""
            try:
                return f"{float(value) * 100:.{decimals}f}%"
            except (ValueError, TypeError):
                return str(value)
        
        def format_db(value):
            """格式化分贝值"""
            try:
                return f"{float(value):.1f} dB"
            except (ValueError, TypeError):
                return str(value)
        
        def format_timestamp(value, format_str="%Y-%m-%d %H:%M:%S"):
            """格式化时间戳"""
            if isinstance(value, (int, float)):
                dt = datetime.fromtimestamp(value)
                return dt.strftime(format_str)
            return str(value)
        
        # 注册过滤器
        self.env.filters['number'] = format_number
        self.env.filters['percentage'] = format_percentage
        self.env.filters['db'] = format_db
        self.env.filters['timestamp'] = format_timestamp
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """渲染模板"""
        try:
            template = self.env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            self.logger.error(f"Jinja2模板渲染失败 {template_name}: {e}")
            raise TemplateError(f"模板渲染失败: {e}")

class TemplateManager:
    """模板管理器"""
    
    def __init__(self, template_dir: str = "templates", use_jinja2: bool = None):
        """
        初始化模板管理器
        
        Args:
            template_dir: 模板目录
            use_jinja2: 是否使用Jinja2，None表示自动检测
        """
        self.template_dir = Path(template_dir)
        
        # 自动选择模板引擎
        if use_jinja2 is None:
            use_jinja2 = JINJA2_AVAILABLE
        
        if use_jinja2 and JINJA2_AVAILABLE:
            self.engine = Jinja2TemplateEngine(template_dir)
            self.engine_type = "Jinja2"
        else:
            self.engine = SimpleTemplateEngine(template_dir)
            self.engine_type = "Simple"
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"使用 {self.engine_type} 模板引擎")
    
    def render(self, template_name: str, context: Dict[str, Any]) -> str:
        """渲染模板"""
        return self.engine.render_template(template_name, context)
    
    def create_template(self, template_name: str, content: str):
        """创建模板文件"""
        template_path = self.template_dir / template_name
        template_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.logger.info(f"模板已创建: {template_path}")
    
    def list_templates(self) -> List[str]:
        """列出所有模板"""
        templates = []
        for template_file in self.template_dir.rglob("*"):
            if template_file.is_file():
                rel_path = template_file.relative_to(self.template_dir)
                templates.append(str(rel_path))
        return sorted(templates)
    
    def template_exists(self, template_name: str) -> bool:
        """检查模板是否存在"""
        template_path = self.template_dir / template_name
        return template_path.exists()

# 全局模板管理器实例
_template_manager = None

def get_template_manager() -> TemplateManager:
    """获取全局模板管理器实例"""
    global _template_manager
    if _template_manager is None:
        _template_manager = TemplateManager()
    return _template_manager

def render_template(template_name: str, context: Dict[str, Any]) -> str:
    """便捷函数：渲染模板"""
    return get_template_manager().render(template_name, context)
