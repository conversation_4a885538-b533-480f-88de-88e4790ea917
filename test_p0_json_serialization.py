#!/usr/bin/env python3
"""
P0任务JSON序列化功能全面验证测试
验证JSON序列化bug是否真的完全解决
"""

import subprocess
import json
import numpy as np
import time
import sys
from pathlib import Path

def run_command(cmd, timeout=60):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd="/Users/<USER>/Documents/Noise"
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "命令超时"

def test_numpy_type_conversion():
    """测试numpy类型转换函数"""
    print("🧪 测试numpy类型转换函数...")
    
    # 导入转换函数
    sys.path.append("/Users/<USER>/Documents/Noise")
    from run_sleep_audio_analysis import convert_numpy_types
    
    # 测试各种numpy类型
    test_cases = [
        # 基本numpy类型
        (np.float32(3.14), float, "float32转换"),
        (np.float64(2.718), float, "float64转换"),
        (np.int32(42), int, "int32转换"),
        (np.int64(123), int, "int64转换"),
        
        # 数组类型
        (np.array([1.1, 2.2, 3.3], dtype=np.float32), list, "float32数组转换"),
        (np.array([1, 2, 3], dtype=np.int64), list, "int64数组转换"),
        (np.array([[1, 2], [3, 4]], dtype=np.float64), list, "2D数组转换"),
        
        # 复杂嵌套结构
        ({
            'float32_val': np.float32(1.5),
            'int64_val': np.int64(100),
            'array_val': np.array([1.1, 2.2], dtype=np.float32),
            'nested': {
                'inner_float': np.float64(3.14),
                'inner_array': np.array([10, 20], dtype=np.int32)
            }
        }, dict, "复杂嵌套结构转换"),
        
        # 特殊值
        (np.float32(np.inf), float, "无穷大转换"),
        (np.float32(-np.inf), float, "负无穷大转换"),
        (np.float32(np.nan), float, "NaN转换"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (input_val, expected_type, description) in enumerate(test_cases, 1):
        try:
            # 转换
            result = convert_numpy_types(input_val)
            
            # 检查类型
            if not isinstance(result, expected_type):
                print(f"❌ 测试 {i} 失败: {description}")
                print(f"   期望类型: {expected_type}, 实际类型: {type(result)}")
                failed += 1
                continue
            
            # 测试JSON序列化
            json_str = json.dumps(result, ensure_ascii=False)
            
            # 测试反序列化
            parsed = json.loads(json_str)
            
            print(f"✅ 测试 {i} 通过: {description}")
            passed += 1
            
        except Exception as e:
            print(f"❌ 测试 {i} 失败: {description}")
            print(f"   错误: {e}")
            failed += 1
    
    print(f"\n📊 numpy类型转换测试结果: {passed} 通过, {failed} 失败")
    return failed == 0

def test_json_output_single_file():
    """测试单文件JSON输出"""
    print("\n🧪 测试单文件JSON输出...")
    
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format json"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode != 0:
        print(f"❌ 单文件JSON输出失败: {stderr}")
        return False
    
    # 检查是否包含JSON输出
    lines = stdout.split('\n')
    json_lines = []
    json_started = False
    
    for line in lines:
        if line.strip().startswith('{'):
            json_started = True
        if json_started:
            json_lines.append(line)
    
    if not json_lines:
        print("❌ 没有找到JSON输出")
        return False
    
    json_content = '\n'.join(json_lines)
    
    try:
        # 解析JSON
        data = json.loads(json_content)
        
        # 验证JSON结构
        required_keys = ['analysis_summary', 'files']
        for key in required_keys:
            if key not in data:
                print(f"❌ JSON缺少必需键: {key}")
                return False
        
        # 验证文件数据
        if not data['files'] or len(data['files']) != 1:
            print("❌ JSON文件数据不正确")
            return False
        
        file_data = data['files'][0]
        required_file_keys = ['filename', 'audio_features', 'safety_assessment', 'sleep_suitability']
        for key in required_file_keys:
            if key not in file_data:
                print(f"❌ JSON文件数据缺少必需键: {key}")
                return False
        
        # 验证数值类型（确保都是Python原生类型）
        audio_features = file_data['audio_features']
        numeric_fields = ['spectral_slope', 'loudness_stability', 'tonal_ratio', 'dynamic_range_db']
        
        for field in numeric_fields:
            if field in audio_features:
                value = audio_features[field]
                if not isinstance(value, (int, float)):
                    print(f"❌ 字段 {field} 类型不正确: {type(value)}")
                    return False
        
        print(f"✅ 单文件JSON输出测试通过")
        print(f"   文件数: {len(data['files'])}")
        print(f"   JSON大小: {len(json_content)} 字符")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ JSON验证失败: {e}")
        return False

def test_json_output_batch():
    """测试批量JSON输出"""
    print("\n🧪 测试批量JSON输出...")
    
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise --all --format json"
    returncode, stdout, stderr = run_command(cmd, timeout=120)
    
    if returncode != 0:
        print(f"❌ 批量JSON输出失败: {stderr}")
        return False
    
    # 提取JSON内容
    lines = stdout.split('\n')
    json_lines = []
    json_started = False
    
    for line in lines:
        if line.strip().startswith('{'):
            json_started = True
        if json_started:
            json_lines.append(line)
    
    json_content = '\n'.join(json_lines)
    
    try:
        # 解析JSON
        data = json.loads(json_content)
        
        # 验证批量数据
        if len(data['files']) < 2:
            print(f"❌ 批量分析文件数不足: {len(data['files'])}")
            return False
        
        # 验证每个文件的数据完整性
        for i, file_data in enumerate(data['files']):
            # 检查必需字段
            required_keys = ['filename', 'audio_features', 'safety_assessment', 'sleep_suitability']
            for key in required_keys:
                if key not in file_data:
                    print(f"❌ 文件 {i+1} 缺少必需键: {key}")
                    return False
            
            # 检查数值类型
            sleep_score = file_data['sleep_suitability']['overall_score']
            if not isinstance(sleep_score, (int, float)):
                print(f"❌ 文件 {i+1} 睡眠得分类型错误: {type(sleep_score)}")
                return False
        
        print(f"✅ 批量JSON输出测试通过")
        print(f"   文件数: {len(data['files'])}")
        print(f"   JSON大小: {len(json_content)} 字符")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 批量JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 批量JSON验证失败: {e}")
        return False

def test_json_with_user_group():
    """测试带用户群体的JSON输出"""
    print("\n🧪 测试带用户群体的JSON输出...")
    
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format json --user-group infant"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode != 0:
        print(f"❌ 用户群体JSON输出失败: {stderr}")
        return False
    
    # 提取JSON内容
    lines = stdout.split('\n')
    json_lines = []
    json_started = False
    
    for line in lines:
        if line.strip().startswith('{'):
            json_started = True
        if json_started:
            json_lines.append(line)
    
    json_content = '\n'.join(json_lines)
    
    try:
        data = json.loads(json_content)
        
        # 检查是否包含个性化推荐
        file_data = data['files'][0]
        if 'personalized_recommendation' not in file_data:
            print("❌ 缺少个性化推荐数据")
            return False
        
        rec_data = file_data['personalized_recommendation']
        if rec_data['user_group'] != '婴幼儿':  # JSON输出使用中文名称
            print(f"❌ 用户群体不正确: {rec_data['user_group']}")
            return False
        
        # 检查推荐数据的数值类型
        if not isinstance(rec_data['suitability_score'], (int, float)):
            print(f"❌ 推荐得分类型错误: {type(rec_data['suitability_score'])}")
            return False
        
        print(f"✅ 用户群体JSON输出测试通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 用户群体JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 用户群体JSON验证失败: {e}")
        return False

def test_error_handling():
    """测试错误处理和降级机制"""
    print("\n🧪 测试错误处理和降级机制...")
    
    # 测试不存在的文件
    cmd = "python3 run_sleep_audio_analysis.py nonexistent_file.wav --format json"
    returncode, stdout, stderr = run_command(cmd)
    
    # 应该返回错误，但不应该崩溃
    if returncode == 0:
        print("⚠️ 不存在文件的处理可能需要改进")
    else:
        print("✅ 不存在文件的错误处理正常")
    
    return True

def test_performance():
    """测试JSON输出性能"""
    print("\n🧪 测试JSON输出性能...")
    
    # 测试单文件性能
    start_time = time.time()
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format json"
    returncode, stdout, stderr = run_command(cmd)
    end_time = time.time()
    
    if returncode != 0:
        print(f"❌ JSON性能测试失败: {stderr}")
        return False
    
    json_time = end_time - start_time
    
    # 测试文本格式性能作为对比
    start_time = time.time()
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format text"
    returncode, stdout, stderr = run_command(cmd)
    end_time = time.time()
    
    if returncode != 0:
        print(f"❌ 文本格式性能测试失败: {stderr}")
        return False
    
    text_time = end_time - start_time
    
    # 计算性能比较
    performance_ratio = json_time / text_time
    
    print(f"📊 性能测试结果:")
    print(f"   JSON格式: {json_time:.2f}秒")
    print(f"   文本格式: {text_time:.2f}秒")
    print(f"   性能比: {performance_ratio:.2f}")
    
    # JSON性能应该不超过文本格式的150%
    if performance_ratio > 1.5:
        print(f"⚠️ JSON性能可能需要优化 (比文本格式慢 {(performance_ratio-1)*100:.1f}%)")
        return False
    
    print("✅ JSON性能测试通过")
    return True

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性...")
    
    # 测试其他格式是否受影响
    formats = ['text', 'markdown']
    
    for fmt in formats:
        cmd = f"python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format {fmt}"
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode != 0:
            print(f"❌ {fmt}格式受到影响: {stderr}")
            return False
        
        if "智能睡眠音频评估系统" not in stdout:
            print(f"❌ {fmt}格式输出异常")
            return False
        
        print(f"✅ {fmt}格式正常工作")
    
    return True

def main():
    """主测试函数"""
    print("🧠 P0任务JSON序列化功能全面验证测试")
    print("=" * 60)
    
    tests = [
        ("numpy类型转换", test_numpy_type_conversion),
        ("单文件JSON输出", test_json_output_single_file),
        ("批量JSON输出", test_json_output_batch),
        ("用户群体JSON输出", test_json_with_user_group),
        ("错误处理机制", test_error_handling),
        ("JSON输出性能", test_performance),
        ("向后兼容性", test_backward_compatibility),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔬 运行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                failed += 1
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - 异常: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{passed+failed} 通过")
    print(f"🎯 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("🎉 所有JSON序列化测试通过！P0任务完全解决")
        return 0
    else:
        print("❌ 部分JSON序列化测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
