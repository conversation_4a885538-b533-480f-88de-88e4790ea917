{"system": {"version": "2.0", "debug_mode": false, "log_level": "INFO", "max_file_size_mb": 100, "supported_formats": ["wav", "mp3", "flac", "ogg", "ogm", "m4a", "aac"]}, "analysis": {"audio_processing": {"default_sample_rate": null, "mono_conversion": true, "normalization": true}, "spectral_analysis": {"frame_size": 4096, "hop_length": 2048, "overlap_ratio": 0.5, "window_type": "hann"}, "bark_analysis": {"num_bands": 24, "freq_range": [20, 20000]}, "a_weighting": {"enabled": true, "reference_freq": 1000}, "dynamic_range": {"frame_duration_ms": 100, "percentile_low": 10, "percentile_high": 90}}, "noise_types": {"white": {"spectral_slope_range": [-0.2, 0.2], "effectiveness": 0.33, "description": "平坦频谱的噪音"}, "pink": {"spectral_slope_range": [-1.2, -0.8], "effectiveness": 0.82, "description": "1/f频谱的噪音，睡眠效果最佳"}, "brown": {"spectral_slope_range": [-2.2, -1.8], "effectiveness": 0.65, "description": "低频丰富的噪音"}, "green": {"mid_freq_range": [400, 800], "dominance_threshold": 1.5, "effectiveness": 0.55, "experimental": true, "description": "中频增强的噪音"}, "deep_red": {"spectral_slope_range": [-3.0, -2.5], "effectiveness": 0.45, "description": "极低频噪音"}}, "safety_thresholds": {"adult": {"max_db": 60, "min_distance_cm": 30, "max_duration_hours": 8, "recommended_volume_range": [45, 60]}, "infant": {"max_db": 50, "min_distance_cm": 200, "max_duration_hours": 1, "recommended_volume_range": [35, 45], "special_considerations": ["仅入睡阶段使用", "严格限制音量", "保持安全距离"]}, "elderly": {"max_db": 55, "min_distance_cm": 50, "max_duration_hours": 6, "recommended_volume_range": [40, 55], "preferences": ["低频丰富声音", "粉噪音优先"]}, "insomnia": {"max_db": 60, "min_distance_cm": 30, "max_duration_hours": 8, "recommended_volume_range": [45, 58], "therapy_notes": ["建立睡眠仪式感", "避免过度依赖"]}}, "green_noise_safety": {"adult": {"max_db": 60, "min_distance_cm": 30, "max_duration_hours": 8, "special_considerations": ["避免过度依赖", "注意中频敏感性"]}, "infant": {"max_db": 45, "min_distance_cm": 200, "max_duration_hours": 2, "special_considerations": ["中频可能影响听觉发育", "严格限制使用时长", "仅入睡阶段使用"]}, "elderly": {"max_db": 55, "min_distance_cm": 50, "max_duration_hours": 6, "special_considerations": ["中频丰富有助听觉刺激", "注意听力保护"]}, "insomnia": {"max_db": 58, "min_distance_cm": 30, "max_duration_hours": 8, "special_considerations": ["作为粉噪音替代选择", "建立睡眠仪式感"]}}, "sleep_evaluation": {"scoring_weights": {"stability_factor": 0.3, "tonal_factor": 0.3, "dynamic_factor": 0.25, "spectrum_factor": 0.15}, "noise_type_factors": {"pink": 1.0, "brown": 0.8, "green": 0.7, "deep_red": 0.6, "white": 0.4, "complex": 0.5}, "source_factors": {"natural": 1.2, "mechanical": 0.8, "synthetic": 1.0}, "quality_thresholds": {"max_dynamic_range_db": 10, "max_tonal_ratio": 5, "min_stability_percent": 95, "max_disruption_risk": 0.3}}, "personalized_recommendations": {"recommendation_thresholds": {"excellent": 80, "good": 60, "acceptable": 40, "poor": 20}, "user_group_adjustments": {"infant": {"volume_reduction": 10, "distance_increase": 170, "duration_limit": 1.0, "timing": "sleep_onset_only"}, "elderly": {"volume_preference": "low_freq_enhanced", "timing": "full_night_if_needed"}, "insomnia": {"therapy_mode": true, "ritual_emphasis": true}}}, "report_generation": {"default_format": "text", "supported_formats": ["text", "json", "markdown", "csv"], "templates": {"detailed_analysis": true, "scientific_references": true, "safety_warnings": true, "personalized_recommendations": true}, "file_naming": {"timestamp_format": "%Y%m%d_%H%M%S", "timezone": "Asia/Shanghai", "prefix": "analysis_report"}, "content_options": {"include_technical_details": true, "include_comparison_charts": true, "include_usage_guidelines": true, "max_recommendations_per_group": 5}}, "performance": {"cache": {"enabled": true, "max_size_mb": 256, "ttl_seconds": 3600}, "parallel_processing": {"enabled": true, "max_workers": 4, "chunk_size": 1}, "memory_management": {"max_memory_mb": 512, "garbage_collection": true}}, "hot_reload": {"enabled": false, "watch_files": ["config/*.yaml", "config/*.json", "templates/*.j2"], "reload_delay_seconds": 1, "backup_on_reload": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/sleep_audio_system.log", "max_file_size_mb": 10, "backup_count": 5, "console_output": true}, "validation": {"audio_file": {"min_duration_seconds": 1, "max_duration_seconds": 3600, "min_sample_rate": 8000, "max_sample_rate": 192000}, "parameters": {"volume_range": [0, 120], "distance_range": [10, 1000], "duration_range": [0.1, 24]}}}