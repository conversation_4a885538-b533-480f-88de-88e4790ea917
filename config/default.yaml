# 智能睡眠音频评估系统 - 默认配置文件
# 版本: 2.0
# 创建时间: 2025-06-25

# 系统基础配置
system:
  version: "2.0"
  debug_mode: false
  log_level: "INFO"
  max_file_size_mb: 100
  supported_formats: ["wav", "mp3", "flac", "ogg", "ogm", "m4a", "aac"]

# 音频分析参数配置
analysis:
  # 基础音频处理参数
  audio_processing:
    default_sample_rate: null  # null表示使用原始采样率
    mono_conversion: true
    normalization: true
    
  # 频谱分析参数
  spectral_analysis:
    frame_size: 4096
    hop_length: 2048
    overlap_ratio: 0.5
    window_type: "hann"
    
  # Bark尺度分析参数
  bark_analysis:
    num_bands: 24
    freq_range: [20, 20000]
    
  # A-weighting参数
  a_weighting:
    enabled: true
    reference_freq: 1000
    
  # 动态范围分析
  dynamic_range:
    frame_duration_ms: 100
    percentile_low: 10
    percentile_high: 90

# 噪音类型识别配置
noise_types:
  # 白噪音
  white:
    spectral_slope_range: [-0.2, 0.2]
    effectiveness: 0.33
    description: "平坦频谱的噪音"
    
  # 粉噪音
  pink:
    spectral_slope_range: [-1.2, -0.8]
    effectiveness: 0.82
    description: "1/f频谱的噪音，睡眠效果最佳"
    
  # 棕噪音
  brown:
    spectral_slope_range: [-2.2, -1.8]
    effectiveness: 0.65
    description: "低频丰富的噪音"
    
  # 绿噪音
  green:
    mid_freq_range: [400, 800]
    dominance_threshold: 1.5
    effectiveness: 0.55
    experimental: true
    description: "中频增强的噪音"
    
  # 深红噪音
  deep_red:
    spectral_slope_range: [-3.0, -2.5]
    effectiveness: 0.45
    description: "极低频噪音"

# 安全阈值配置
safety_thresholds:
  # 成人群体
  adult:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    recommended_volume_range: [45, 60]
    
  # 婴幼儿群体
  infant:
    max_db: 50
    min_distance_cm: 200
    max_duration_hours: 1
    recommended_volume_range: [35, 45]
    special_considerations:
      - "仅入睡阶段使用"
      - "严格限制音量"
      - "保持安全距离"
    
  # 老年人群体
  elderly:
    max_db: 55
    min_distance_cm: 50
    max_duration_hours: 6
    recommended_volume_range: [40, 55]
    preferences:
      - "低频丰富声音"
      - "粉噪音优先"
    
  # 失眠患者群体
  insomnia:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    recommended_volume_range: [45, 58]
    therapy_notes:
      - "建立睡眠仪式感"
      - "避免过度依赖"

# 绿噪音专用安全配置
green_noise_safety:
  adult:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    special_considerations:
      - "避免过度依赖"
      - "注意中频敏感性"
      
  infant:
    max_db: 45
    min_distance_cm: 200
    max_duration_hours: 2
    special_considerations:
      - "中频可能影响听觉发育"
      - "严格限制使用时长"
      - "仅入睡阶段使用"
      
  elderly:
    max_db: 55
    min_distance_cm: 50
    max_duration_hours: 6
    special_considerations:
      - "中频丰富有助听觉刺激"
      - "注意听力保护"
      
  insomnia:
    max_db: 58
    min_distance_cm: 30
    max_duration_hours: 8
    special_considerations:
      - "作为粉噪音替代选择"
      - "建立睡眠仪式感"

# 睡眠适用性评估配置
sleep_evaluation:
  # 评分权重配置
  scoring_weights:
    stability_factor: 0.30    # 响度稳定性权重
    tonal_factor: 0.30        # 音调性权重
    dynamic_factor: 0.25      # 动态范围权重
    spectrum_factor: 0.15     # 频谱适合度权重
    
  # 噪音类型评分因子
  noise_type_factors:
    pink: 1.0      # 粉噪音最佳
    brown: 0.8     # 棕噪音良好
    green: 0.7     # 绿噪音中等
    deep_red: 0.6  # 深红噪音一般
    white: 0.4     # 白噪音较差
    complex: 0.5   # 复杂噪音默认
    
  # 音频来源评分因子
  source_factors:
    natural: 1.2      # 自然声音加分
    mechanical: 0.8   # 机械声音减分
    synthetic: 1.0    # 合成声音标准
    
  # 质量阈值
  quality_thresholds:
    max_dynamic_range_db: 10
    max_tonal_ratio: 5
    min_stability_percent: 95
    max_disruption_risk: 0.3

# 个性化推荐配置
personalized_recommendations:
  # 推荐等级阈值
  recommendation_thresholds:
    excellent: 80
    good: 60
    acceptable: 40
    poor: 20
    
  # 用户群体特定调整
  user_group_adjustments:
    infant:
      volume_reduction: 10  # dB
      distance_increase: 170  # cm
      duration_limit: 1.0   # hours
      timing: "sleep_onset_only"
      
    elderly:
      volume_preference: "low_freq_enhanced"
      timing: "full_night_if_needed"
      
    insomnia:
      therapy_mode: true
      ritual_emphasis: true

# 报告生成配置
report_generation:
  # 默认输出格式
  default_format: "text"
  
  # 支持的输出格式
  supported_formats: ["text", "json", "markdown", "csv"]
  
  # 报告模板配置
  templates:
    detailed_analysis: true
    scientific_references: true
    safety_warnings: true
    personalized_recommendations: true
    
  # 文件命名配置
  file_naming:
    timestamp_format: "%Y%m%d_%H%M%S"
    timezone: "Asia/Shanghai"
    prefix: "analysis_report"
    
  # 内容配置
  content_options:
    include_technical_details: true
    include_comparison_charts: true
    include_usage_guidelines: true
    max_recommendations_per_group: 5

# 性能优化配置
performance:
  # 缓存配置
  cache:
    enabled: true
    max_size_mb: 256
    ttl_seconds: 3600
    
  # 并行处理配置
  parallel_processing:
    enabled: true
    max_workers: 4
    chunk_size: 1
    
  # 内存管理
  memory_management:
    max_memory_mb: 512
    garbage_collection: true

# 热更新配置
hot_reload:
  enabled: true
  watch_files: ["config/*.yaml", "templates/*.j2"]
  reload_delay_seconds: 1
  backup_on_reload: true

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/sleep_audio_system.log"
  max_file_size_mb: 10
  backup_count: 5
  console_output: true

# 验证规则配置
validation:
  # 音频文件验证
  audio_file:
    min_duration_seconds: 1
    max_duration_seconds: 3600
    min_sample_rate: 8000
    max_sample_rate: 192000
    
  # 配置参数验证
  parameters:
    volume_range: [0, 120]
    distance_range: [10, 1000]
    duration_range: [0.1, 24]
