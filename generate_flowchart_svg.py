#!/usr/bin/env python3
"""
智能睡眠音频评估系统 - 决策流程图SVG生成器
将Mermaid流程图代码转换为SVG文件
"""

import os
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime

def get_china_timestamp():
    """获取中国时间戳"""
    return "20250625_113500"  # 与文档保持一致

def create_mermaid_files():
    """创建Mermaid源文件"""
    timestamp = get_china_timestamp()
    
    # 主要决策流程图
    main_flowchart = """graph TD
    A[📋 用户需求分析] --> B{🔍 需求复杂度评估}
    
    B -->|简单需求<br/>单一功能| C{🛠️ 现有工具支持度}
    B -->|中等复杂度<br/>多功能组合| D{⚙️ 参数扩展可行性}
    B -->|高复杂度<br/>新核心逻辑| E{🏗️ 架构影响评估}
    
    C -->|完全支持<br/>≥90%需求| F[✅ 使用现有工具]
    C -->|部分支持<br/>60-89%需求| G{🔧 参数组合充分性}
    C -->|支持不足<br/><60%需求| H[⚠️ 需要功能扩展]
    
    D -->|可通过参数实现| I[🔧 扩展现有工具]
    D -->|需要新逻辑| J{💰 开发成本评估}
    
    E -->|核心逻辑变更| K[🆕 创建专用脚本]
    E -->|可复用核心| L{🔌 插件化可行性}
    
    F --> M[📄 run_sleep_audio_analysis.py]
    G -->|充分| M
    G -->|不充分| I
    H --> I
    I --> N[📄 enhanced_run_sleep_audio_analysis.py]
    J -->|成本低<br/><2人日| I
    J -->|成本高<br/>≥2人日| K
    K --> O[📄 专用分析脚本]
    L -->|可行| P[🔌 插件化扩展]
    L -->|不可行| K
    
    M --> Q[📊 标准分析报告]
    N --> R[📊 增强分析报告]
    O --> S[📊 专业定制报告]
    P --> T[📊 模块化报告系统]
    
    style A fill:#e3f2fd
    style F fill:#e8f5e8
    style I fill:#fff3e0
    style K fill:#ffebee
    style M fill:#e8f5e8
    style N fill:#fff3e0
    style O fill:#ffebee
    style P fill:#f3e5f5"""
    
    # 详细判断条件流程图
    detailed_flowchart = """graph TD
    A1[需求输入] --> B1{需求类型分析}
    
    B1 -->|报告格式需求| C1{格式复杂度}
    B1 -->|分析功能需求| C2{算法复杂度}
    B1 -->|用户体验需求| C3{交互复杂度}
    B1 -->|集成需求| C4{系统复杂度}
    
    C1 -->|标准格式<br/>text/json| D1[现有工具]
    C1 -->|新格式<br/>markdown/html| D2[扩展工具]
    C1 -->|复杂模板<br/>多变量| D3[专用脚本]
    
    C2 -->|现有算法| D1
    C2 -->|参数调整| D2
    C2 -->|新算法| D3
    
    C3 -->|命令行增强| D2
    C3 -->|GUI需求| D3
    C3 -->|Web界面| D4[新系统]
    
    C4 -->|API调用| D2
    C4 -->|数据库集成| D3
    C4 -->|微服务架构| D4
    
    style D1 fill:#c8e6c9
    style D2 fill:#fff3e0
    style D3 fill:#ffcdd2
    style D4 fill:#e1bee7"""
    
    # 创建临时文件
    main_file = f"decision_flowchart_main_{timestamp}.mmd"
    detailed_file = f"decision_flowchart_detailed_{timestamp}.mmd"
    
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(main_flowchart)
    
    with open(detailed_file, 'w', encoding='utf-8') as f:
        f.write(detailed_flowchart)
    
    return main_file, detailed_file

def check_mermaid_cli():
    """检查Mermaid CLI是否已安装"""
    try:
        result = subprocess.run(['mmdc', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Mermaid CLI已安装: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Mermaid CLI未安装")
        return False

def install_mermaid_cli():
    """安装Mermaid CLI"""
    print("📦 正在安装Mermaid CLI...")
    try:
        # 检查npm是否可用
        subprocess.run(['npm', '--version'], capture_output=True, check=True)
        
        # 安装mermaid-cli
        result = subprocess.run(['npm', 'install', '-g', '@mermaid-js/mermaid-cli'], 
                              capture_output=True, text=True, check=True)
        print("✅ Mermaid CLI安装成功")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"❌ Mermaid CLI安装失败: {e}")
        print("请手动安装: npm install -g @mermaid-js/mermaid-cli")
        return False

def generate_svg_files():
    """生成SVG文件"""
    timestamp = get_china_timestamp()
    
    print("🎨 智能睡眠音频评估系统 - 决策流程图SVG生成器")
    print("=" * 60)
    
    # 检查并安装Mermaid CLI
    if not check_mermaid_cli():
        if not install_mermaid_cli():
            print("\n💡 手动安装方法:")
            print("1. 安装Node.js: https://nodejs.org/")
            print("2. 安装Mermaid CLI: npm install -g @mermaid-js/mermaid-cli")
            print("3. 重新运行此脚本")
            return False
    
    # 创建Mermaid源文件
    print("\n📝 创建Mermaid源文件...")
    main_file, detailed_file = create_mermaid_files()
    
    # 生成SVG文件
    svg_files = []
    
    try:
        # 生成主要决策流程图
        main_svg = f"decision_flowchart_main_{timestamp}.svg"
        print(f"🎨 生成主要决策流程图: {main_svg}")
        subprocess.run(['mmdc', '-i', main_file, '-o', main_svg, '-t', 'default'], 
                      check=True)
        svg_files.append(main_svg)
        
        # 生成详细判断条件流程图
        detailed_svg = f"decision_flowchart_detailed_{timestamp}.svg"
        print(f"🎨 生成详细判断条件流程图: {detailed_svg}")
        subprocess.run(['mmdc', '-i', detailed_file, '-o', detailed_svg, '-t', 'default'], 
                      check=True)
        svg_files.append(detailed_svg)
        
        print(f"\n✅ 成功生成 {len(svg_files)} 个SVG文件:")
        for svg_file in svg_files:
            file_size = os.path.getsize(svg_file) / 1024  # KB
            print(f"   📄 {svg_file} ({file_size:.1f} KB)")
        
        # 清理临时文件
        os.remove(main_file)
        os.remove(detailed_file)
        print(f"\n🧹 已清理临时文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ SVG生成失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def main():
    """主函数"""
    success = generate_svg_files()
    
    if success:
        print("\n🎉 SVG文件生成完成!")
        print("\n📋 文件说明:")
        print("• decision_flowchart_main_20250625_113500.svg - 主要决策流程图")
        print("• decision_flowchart_detailed_20250625_113500.svg - 详细判断条件流程图")
        print("\n💡 使用建议:")
        print("• 可在任何支持SVG的应用中打开和编辑")
        print("• 适合插入到文档、演示文稿和网页中")
        print("• 矢量格式确保在任何分辨率下都清晰显示")
    else:
        print("\n❌ SVG文件生成失败!")
        print("\n🔧 替代方案:")
        print("1. 使用在线Mermaid编辑器: https://mermaid.live/")
        print("2. 复制Mermaid代码到编辑器中")
        print("3. 手动导出为SVG格式")

if __name__ == "__main__":
    main()
