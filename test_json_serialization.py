#!/usr/bin/env python3
"""
智能睡眠音频评估系统 - JSON序列化测试
测试numpy类型转换和JSON序列化功能
"""

import json
import numpy as np
import time
import sys
from pathlib import Path

# 导入转换函数
from run_sleep_audio_analysis import convert_numpy_types

def test_convert_numpy_types():
    """测试numpy类型转换函数"""
    print("🧪 测试numpy类型转换函数...")
    
    # 测试基本numpy类型
    test_cases = [
        # 浮点数类型
        (np.float32(3.14), float, "float32转换"),
        (np.float64(2.718), float, "float64转换"),
        
        # 整数类型
        (np.int32(42), int, "int32转换"),
        (np.int64(123), int, "int64转换"),
        
        # 数组类型
        (np.array([1, 2, 3]), list, "ndarray转换"),
        (np.array([[1, 2], [3, 4]]), list, "2D ndarray转换"),
        
        # 复合类型
        ({
            'float32_val': np.float32(1.5),
            'int64_val': np.int64(100),
            'array_val': np.array([1, 2, 3])
        }, dict, "字典嵌套转换"),
        
        ([np.float32(1.1), np.int32(2), np.array([3, 4])], list, "列表嵌套转换"),
        
        # 元组类型
        ((np.float32(1.0), np.int32(2)), tuple, "元组转换"),
        
        # 普通Python类型（应保持不变）
        (42, int, "普通int保持不变"),
        (3.14, float, "普通float保持不变"),
        ("hello", str, "字符串保持不变"),
        ([1, 2, 3], list, "普通list保持不变"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (input_val, expected_type, description) in enumerate(test_cases, 1):
        try:
            result = convert_numpy_types(input_val)
            
            # 检查类型
            if not isinstance(result, expected_type):
                print(f"❌ 测试 {i} 失败: {description}")
                print(f"   期望类型: {expected_type}, 实际类型: {type(result)}")
                failed += 1
                continue
            
            # 检查JSON序列化
            json_str = json.dumps(result, ensure_ascii=False)
            
            # 检查反序列化
            parsed = json.loads(json_str)
            
            print(f"✅ 测试 {i} 通过: {description}")
            passed += 1
            
        except Exception as e:
            print(f"❌ 测试 {i} 失败: {description}")
            print(f"   错误: {e}")
            failed += 1
    
    print(f"\n📊 测试结果: {passed} 通过, {failed} 失败")
    return failed == 0

def test_performance():
    """测试转换性能"""
    print("\n⚡ 测试转换性能...")
    
    # 创建大型测试数据
    large_data = {
        'float_array': np.random.random(1000).astype(np.float32),
        'int_array': np.random.randint(0, 100, 1000).astype(np.int32),
        'nested_dict': {
            f'key_{i}': {
                'value': np.float64(i * 3.14),
                'count': np.int64(i),
                'data': np.random.random(10).astype(np.float32)
            }
            for i in range(100)
        }
    }
    
    # 测试转换时间
    start_time = time.time()
    converted = convert_numpy_types(large_data)
    conversion_time = time.time() - start_time
    
    # 测试JSON序列化时间
    start_time = time.time()
    json_str = json.dumps(converted, ensure_ascii=False)
    serialization_time = time.time() - start_time
    
    total_time = conversion_time + serialization_time
    
    print(f"📈 性能测试结果:")
    print(f"   转换时间: {conversion_time:.3f}秒")
    print(f"   序列化时间: {serialization_time:.3f}秒")
    print(f"   总时间: {total_time:.3f}秒")
    print(f"   JSON大小: {len(json_str):,} 字符")
    
    # 性能要求：总时间应小于1秒
    if total_time < 1.0:
        print("✅ 性能测试通过 (< 1秒)")
        return True
    else:
        print("❌ 性能测试失败 (≥ 1秒)")
        return False

def test_real_audio_analysis():
    """测试真实音频分析的JSON输出"""
    print("\n🎵 测试真实音频分析JSON输出...")
    
    try:
        from smart_sleep_audio_system import SmartSleepAudioSystem
        
        # 检查测试音频文件
        test_files = [
            "Sounds/Noise/white-noise.wav",
            "Sounds/Noise/pink-noise.wav",
            "Sounds/Noise/brown-noise.wav"
        ]
        
        available_files = [f for f in test_files if Path(f).exists()]
        
        if not available_files:
            print("⚠️  没有找到测试音频文件，跳过真实分析测试")
            return True
        
        system = SmartSleepAudioSystem()
        
        for audio_file in available_files[:1]:  # 只测试第一个文件以节省时间
            print(f"   分析文件: {audio_file}")
            
            # 分析音频
            start_time = time.time()
            result = system.analyze_audio_file(audio_file)
            analysis_time = time.time() - start_time
            
            # 转换为字典格式（模拟JSON输出过程）
            result_dict = {
                'filename': Path(audio_file).name,
                'audio_features': {
                    'spectral_slope': result.audio_features.spectral_slope,
                    'loudness_stability': result.audio_features.loudness_stability,
                    'tonal_ratio': result.audio_features.tonal_ratio,
                    'dynamic_range_db': result.audio_features.dynamic_range_db,
                    'duration_seconds': result.audio_features.duration_seconds
                },
                'sleep_suitability': {
                    'overall_score': result.sleep_suitability.overall_score,
                    'effectiveness_prediction': result.sleep_suitability.effectiveness_prediction,
                    'disruption_risk': result.sleep_suitability.disruption_risk,
                    'comfort_level': result.sleep_suitability.comfort_level
                }
            }
            
            # 测试转换和序列化
            start_time = time.time()
            converted = convert_numpy_types(result_dict)
            json_str = json.dumps(converted, ensure_ascii=False, indent=2)
            json_time = time.time() - start_time
            
            print(f"   ✅ 分析时间: {analysis_time:.2f}秒")
            print(f"   ✅ JSON处理时间: {json_time:.3f}秒")
            print(f"   ✅ JSON大小: {len(json_str):,} 字符")
            
            # 验证JSON可以正确解析
            parsed = json.loads(json_str)
            print(f"   ✅ JSON解析成功")
            
        return True
        
    except Exception as e:
        print(f"❌ 真实分析测试失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    edge_cases = [
        (None, "None值"),
        ({}, "空字典"),
        ([], "空列表"),
        ((), "空元组"),
        (np.array([]), "空数组"),
        (np.nan, "NaN值"),
        (np.inf, "无穷大"),
        (-np.inf, "负无穷大"),
        (np.array([np.nan, np.inf, -np.inf]), "包含特殊值的数组"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (input_val, description) in enumerate(edge_cases, 1):
        try:
            result = convert_numpy_types(input_val)
            json_str = json.dumps(result, ensure_ascii=False)
            parsed = json.loads(json_str)
            
            print(f"✅ 边界测试 {i} 通过: {description}")
            passed += 1
            
        except Exception as e:
            print(f"❌ 边界测试 {i} 失败: {description}")
            print(f"   错误: {e}")
            failed += 1
    
    print(f"\n📊 边界测试结果: {passed} 通过, {failed} 失败")
    return failed == 0

def main():
    """主测试函数"""
    print("🧠 智能睡眠音频评估系统 - JSON序列化测试套件")
    print("=" * 60)
    
    all_passed = True
    
    # 运行所有测试
    tests = [
        ("基础类型转换", test_convert_numpy_types),
        ("性能测试", test_performance),
        ("边界情况测试", test_edge_cases),
        ("真实音频分析", test_real_audio_analysis),
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔬 运行测试: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {e}")
            all_passed = False
    
    # 总结
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！JSON序列化功能正常工作")
        print("✅ P0任务：JSON序列化bug修复 - 完成")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
