#!/usr/bin/env python3
"""
测试配置管理器功能
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import Confi<PERSON><PERSON><PERSON><PERSON>, get_config_manager, get_config

def test_config_manager():
    """测试配置管理器基本功能"""
    print("🧪 测试配置管理器...")
    
    try:
        # 测试配置管理器初始化
        print("1. 测试配置管理器初始化...")
        config_manager = ConfigManager()
        print("   ✅ 配置管理器初始化成功")
        
        # 测试获取配置值
        print("2. 测试获取配置值...")
        system_version = config_manager.get('system.version')
        print(f"   系统版本: {system_version}")
        
        max_db_adult = config_manager.get('safety_thresholds.adult.max_db')
        print(f"   成人最大音量: {max_db_adult} dB")
        
        pink_effectiveness = config_manager.get('noise_types.pink.effectiveness')
        print(f"   粉噪音有效性: {pink_effectiveness}")
        
        # 测试默认值
        non_existent = config_manager.get('non.existent.key', 'default_value')
        print(f"   不存在的键的默认值: {non_existent}")
        print("   ✅ 配置值获取测试通过")
        
        # 测试设置配置值
        print("3. 测试设置配置值...")
        config_manager.set('test.key', 'test_value')
        test_value = config_manager.get('test.key')
        print(f"   设置的测试值: {test_value}")
        assert test_value == 'test_value', "设置配置值失败"
        print("   ✅ 配置值设置测试通过")
        
        # 测试嵌套配置
        print("4. 测试嵌套配置...")
        config_manager.set('nested.deep.value', 42)
        nested_value = config_manager.get('nested.deep.value')
        print(f"   嵌套配置值: {nested_value}")
        assert nested_value == 42, "嵌套配置设置失败"
        print("   ✅ 嵌套配置测试通过")
        
        # 测试全局配置管理器
        print("5. 测试全局配置管理器...")
        global_config = get_config('system.version')
        print(f"   全局配置获取: {global_config}")
        print("   ✅ 全局配置管理器测试通过")
        
        # 测试配置验证
        print("6. 测试配置验证...")
        all_config = config_manager.get_all_config()
        required_sections = ['system', 'analysis', 'noise_types', 'safety_thresholds']
        for section in required_sections:
            assert section in all_config, f"缺少必需的配置节: {section}"
        print("   ✅ 配置验证测试通过")
        
        # 显示一些关键配置
        print("\n📋 关键配置信息:")
        print(f"   系统版本: {config_manager.get('system.version')}")
        print(f"   调试模式: {config_manager.get('system.debug_mode')}")
        print(f"   支持的格式: {config_manager.get('system.supported_formats')}")
        print(f"   热更新启用: {config_manager.get('hot_reload.enabled')}")
        
        print("\n🎯 噪音类型有效性:")
        for noise_type in ['white', 'pink', 'brown', 'green', 'deep_red']:
            effectiveness = config_manager.get(f'noise_types.{noise_type}.effectiveness')
            print(f"   {noise_type}: {effectiveness}")
        
        print("\n🛡️ 安全阈值 (成人):")
        adult_safety = config_manager.get('safety_thresholds.adult')
        for key, value in adult_safety.items():
            print(f"   {key}: {value}")
        
        print("\n✅ 所有测试通过！配置管理器工作正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_formats():
    """测试不同配置文件格式"""
    print("\n🧪 测试配置文件格式...")
    
    # 检查YAML和JSON文件是否都存在
    yaml_file = Path("config/default.yaml")
    json_file = Path("config/default.json")
    
    print(f"YAML文件存在: {yaml_file.exists()}")
    print(f"JSON文件存在: {json_file.exists()}")
    
    if json_file.exists():
        print("✅ JSON配置文件可用")
        
        # 测试JSON配置加载
        try:
            config_manager = ConfigManager(default_config="default.json")
            version = config_manager.get('system.version')
            print(f"从JSON加载的版本: {version}")
            print("✅ JSON配置加载成功")
        except Exception as e:
            print(f"❌ JSON配置加载失败: {e}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始配置管理器测试")
    print("=" * 50)
    
    # 确保配置目录存在
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # 运行测试
    success = True
    success &= test_config_manager()
    success &= test_config_file_formats()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！配置系统准备就绪。")
    else:
        print("❌ 部分测试失败，请检查配置。")
    
    return success

if __name__ == "__main__":
    main()
