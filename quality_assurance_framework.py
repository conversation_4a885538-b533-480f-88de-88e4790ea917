#!/usr/bin/env python3
"""
智能睡眠音频评估系统 - 质量保证框架
基于P1任务经验教训设计的完整质量保证体系
"""

import json
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class TestResult(Enum):
    PASS = "PASS"
    FAIL = "FAIL"
    WARNING = "WARNING"

@dataclass
class QualityMetric:
    name: str
    score: float
    threshold: float
    status: TestResult
    details: str

class FunctionCompletenessChecker:
    """功能完整性检查器"""
    
    def __init__(self):
        self.required_features = {
            'markdown_output': {
                'sections': [
                    '报告信息', '分析结果汇总', '技术参数对比分析', 
                    '详细分析结果', '性能统计', '科学依据与使用建议'
                ],
                'parameters': ['detailed', 'comparison', 'template', 'focus_user_group'],
                'templates': ['standard', 'research', 'clinical', 'consumer']
            },
            'user_recommendations': {
                'groups': ['成人', '婴幼儿', '老年人', '失眠患者'],
                'fields': ['推荐得分', '使用建议', '主要益处', '潜在风险', '科学依据']
            },
            'technical_comparison': {
                'parameters': ['频谱斜率', '响度稳定性', '动态范围', '音调峰值比', '效果预测'],
                'format': 'table'
            }
        }
    
    def check_markdown_output(self, output_content: str) -> QualityMetric:
        """检查Markdown输出完整性"""
        required_sections = self.required_features['markdown_output']['sections']
        found_sections = []
        
        for section in required_sections:
            if section in output_content:
                found_sections.append(section)
        
        completeness = len(found_sections) / len(required_sections)
        
        return QualityMetric(
            name="Markdown输出完整性",
            score=completeness,
            threshold=1.0,
            status=TestResult.PASS if completeness >= 1.0 else TestResult.FAIL,
            details=f"找到 {len(found_sections)}/{len(required_sections)} 个必需章节"
        )
    
    def check_user_recommendations(self, output_content: str) -> QualityMetric:
        """检查用户群体推荐完整性"""
        required_groups = self.required_features['user_recommendations']['groups']
        found_groups = []
        
        for group in required_groups:
            if f"**{group}**:" in output_content:
                found_groups.append(group)
        
        completeness = len(found_groups) / len(required_groups)
        
        return QualityMetric(
            name="用户群体推荐完整性",
            score=completeness,
            threshold=1.0,
            status=TestResult.PASS if completeness >= 1.0 else TestResult.FAIL,
            details=f"找到 {len(found_groups)}/{len(required_groups)} 个用户群体推荐"
        )

class OutputComparator:
    """输出对比器"""
    
    def __init__(self):
        self.comparison_metrics = [
            'content_length',
            'section_count',
            'data_completeness',
            'structure_similarity'
        ]
    
    def compare_outputs(self, baseline_file: str, current_file: str) -> List[QualityMetric]:
        """对比输出文件"""
        metrics = []
        
        try:
            with open(baseline_file, 'r', encoding='utf-8') as f:
                baseline_content = f.read()
            
            with open(current_file, 'r', encoding='utf-8') as f:
                current_content = f.read()
            
            # 内容长度对比
            length_ratio = len(current_content) / len(baseline_content)
            metrics.append(QualityMetric(
                name="内容长度对比",
                score=length_ratio,
                threshold=0.8,
                status=TestResult.PASS if length_ratio >= 0.8 else TestResult.FAIL,
                details=f"当前/基准长度比: {length_ratio:.2f}"
            ))
            
            # 章节数量对比
            baseline_sections = baseline_content.count('##')
            current_sections = current_content.count('##')
            section_ratio = current_sections / baseline_sections if baseline_sections > 0 else 0
            
            metrics.append(QualityMetric(
                name="章节数量对比",
                score=section_ratio,
                threshold=1.0,
                status=TestResult.PASS if section_ratio >= 1.0 else TestResult.FAIL,
                details=f"当前/基准章节数: {current_sections}/{baseline_sections}"
            ))
            
        except Exception as e:
            metrics.append(QualityMetric(
                name="文件对比",
                score=0.0,
                threshold=1.0,
                status=TestResult.FAIL,
                details=f"对比失败: {e}"
            ))
        
        return metrics

class PerformanceBenchmark:
    """性能基准测试"""
    
    def __init__(self):
        self.baseline_times = {
            'single_file': 30.0,  # 秒
            'batch_analysis': 20.0  # 秒/文件
        }
    
    def benchmark_single_file(self, audio_file: str) -> QualityMetric:
        """单文件分析性能测试"""
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'python3', 'run_sleep_audio_analysis.py', 
                audio_file, '--format', 'markdown', '--detailed'
            ], capture_output=True, text=True, timeout=60)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            performance_ratio = self.baseline_times['single_file'] / execution_time
            
            return QualityMetric(
                name="单文件分析性能",
                score=performance_ratio,
                threshold=1.0,
                status=TestResult.PASS if performance_ratio >= 1.0 else TestResult.WARNING,
                details=f"执行时间: {execution_time:.2f}秒 (基准: {self.baseline_times['single_file']}秒)"
            )
            
        except Exception as e:
            return QualityMetric(
                name="单文件分析性能",
                score=0.0,
                threshold=1.0,
                status=TestResult.FAIL,
                details=f"性能测试失败: {e}"
            )

class IntegrationTestSuite:
    """集成测试套件"""
    
    def __init__(self):
        self.completeness_checker = FunctionCompletenessChecker()
        self.output_comparator = OutputComparator()
        self.performance_benchmark = PerformanceBenchmark()
        
    def run_full_test_suite(self, test_audio_files: List[str]) -> Dict[str, List[QualityMetric]]:
        """运行完整测试套件"""
        results = {
            'functionality': [],
            'comparison': [],
            'performance': []
        }
        
        print("🧪 开始运行完整测试套件...")
        
        # 1. 功能完整性测试
        print("📋 测试功能完整性...")
        for audio_file in test_audio_files:
            output_file = self._generate_test_output(audio_file)
            if output_file:
                with open(output_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                results['functionality'].extend([
                    self.completeness_checker.check_markdown_output(content),
                    self.completeness_checker.check_user_recommendations(content)
                ])
        
        # 2. 性能测试
        print("⚡ 测试性能表现...")
        if test_audio_files:
            results['performance'].append(
                self.performance_benchmark.benchmark_single_file(test_audio_files[0])
            )
        
        print("✅ 测试套件运行完成")
        return results
    
    def _generate_test_output(self, audio_file: str) -> str:
        """生成测试输出文件"""
        try:
            output_file = f"test_output_{int(time.time())}.md"
            
            result = subprocess.run([
                'python3', 'run_sleep_audio_analysis.py',
                audio_file, '--format', 'markdown', '--detailed', '--auto-name'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                # 从输出中提取文件名
                lines = result.stdout.split('\n')
                for line in lines:
                    if "报告已保存到:" in line:
                        return line.split(": ")[1]
            
            return None
            
        except Exception as e:
            print(f"❌ 生成测试输出失败: {e}")
            return None

class QualityGate:
    """质量门禁"""
    
    def __init__(self):
        self.gate_criteria = {
            'functionality': {
                'threshold': 1.0,
                'weight': 0.4
            },
            'comparison': {
                'threshold': 0.9,
                'weight': 0.3
            },
            'performance': {
                'threshold': 0.8,
                'weight': 0.3
            }
        }
    
    def evaluate(self, test_results: Dict[str, List[QualityMetric]]) -> Tuple[bool, str, Dict]:
        """评估是否通过质量门禁"""
        category_scores = {}
        overall_score = 0.0
        
        for category, metrics in test_results.items():
            if not metrics:
                category_scores[category] = 0.0
                continue
                
            # 计算类别平均分
            total_score = sum(metric.score for metric in metrics)
            category_score = total_score / len(metrics)
            category_scores[category] = category_score
            
            # 计算加权总分
            weight = self.gate_criteria.get(category, {}).get('weight', 0.0)
            overall_score += category_score * weight
        
        # 检查是否通过门禁
        passed = True
        failed_categories = []
        
        for category, score in category_scores.items():
            threshold = self.gate_criteria.get(category, {}).get('threshold', 0.8)
            if score < threshold:
                passed = False
                failed_categories.append(f"{category}: {score:.2f} < {threshold}")
        
        # 生成报告
        if passed:
            message = f"✅ 质量门禁通过 (总分: {overall_score:.2f})"
        else:
            message = f"❌ 质量门禁失败 (总分: {overall_score:.2f})\n失败项目: {', '.join(failed_categories)}"
        
        return passed, message, {
            'overall_score': overall_score,
            'category_scores': category_scores,
            'failed_categories': failed_categories
        }

def main():
    """主函数 - 运行质量保证流程"""
    print("🧠 智能睡眠音频评估系统 - 质量保证框架")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        "Sounds/Noise/white-noise.wav",
        "Sounds/Noise/pink-noise.wav"
    ]
    
    # 运行测试套件
    test_suite = IntegrationTestSuite()
    results = test_suite.run_full_test_suite(test_files)
    
    # 评估质量门禁
    quality_gate = QualityGate()
    passed, message, details = quality_gate.evaluate(results)
    
    # 输出结果
    print("\n📊 测试结果汇总:")
    print("-" * 40)
    
    for category, metrics in results.items():
        print(f"\n{category.upper()}:")
        for metric in metrics:
            status_icon = "✅" if metric.status == TestResult.PASS else "❌" if metric.status == TestResult.FAIL else "⚠️"
            print(f"  {status_icon} {metric.name}: {metric.score:.2f} (阈值: {metric.threshold})")
            print(f"     {metric.details}")
    
    print(f"\n{message}")
    
    return 0 if passed else 1

if __name__ == "__main__":
    exit(main())
