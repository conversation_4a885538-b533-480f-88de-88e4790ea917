"""
改进的音频分析算法示例
基于心理声学理论和现代信号处理技术
"""

import numpy as np
import librosa
from scipy import signal
from scipy.stats import linregress
import warnings
warnings.filterwarnings('ignore')

class ImprovedAudioAnalyzer:
    """改进的音频分析器，基于心理声学理论"""
    
    def __init__(self, sr=22050):
        self.sr = sr
        # Bark频带中心频率 (Hz)
        self.bark_frequencies = np.array([
            50, 150, 250, 350, 450, 570, 700, 840, 1000, 1170,
            1370, 1600, 1850, 2150, 2500, 2900, 3400, 4000, 4800,
            5800, 7000, 8500, 10500, 13500, 18000
        ])
        
    def calculate_a_weighted_rms(self, y):
        """计算A-weighting加权的RMS值"""
        # A-weighting滤波器系数 (简化版本)
        # 实际应用中应使用标准的A-weighting滤波器
        nyquist = self.sr / 2
        
        # 设计A-weighting滤波器 (简化版本)
        # 实际的A-weighting需要更复杂的IIR滤波器设计
        frequencies = np.array([20, 100, 1000, 10000, 20000])
        gains_db = np.array([-70, -20, 0, -10, -40])  # 简化的A-weighting曲线
        
        # 应用频率加权
        stft = librosa.stft(y)
        freqs = librosa.fft_frequencies(sr=self.sr)
        
        # 简化的A-weighting (实际应该使用标准滤波器)
        a_weights = np.interp(freqs, frequencies, gains_db)
        a_weights = 10 ** (a_weights / 20)  # 转换为线性增益
        
        # 应用加权
        weighted_stft = stft * a_weights[:, np.newaxis]
        weighted_signal = librosa.istft(weighted_stft)
        
        # 计算RMS
        rms = np.sqrt(np.mean(weighted_signal**2))
        return rms
    
    def calculate_perceptual_loudness_stability(self, y):
        """基于感知响度的稳定性计算"""
        # 使用较短的时间窗口以更好地捕捉变化
        frame_length = int(0.02 * self.sr)  # 20ms窗口
        hop_length = int(0.01 * self.sr)    # 10ms跳跃
        
        # 计算每帧的A-weighted RMS
        frames = librosa.util.frame(y, frame_length=frame_length, 
                                   hop_length=hop_length, axis=0)
        
        loudness_values = []
        for frame in frames.T:
            if len(frame) == frame_length:
                loudness = self.calculate_a_weighted_rms(frame)
                if loudness > 0:
                    # 转换为响度级 (简化版本)
                    loudness_level = 20 * np.log10(loudness) + 94  # 简化的响度级计算
                    loudness_values.append(loudness_level)
        
        if len(loudness_values) < 2:
            return 1.0
            
        loudness_values = np.array(loudness_values)
        
        # 计算变异系数，但使用更合理的基准
        mean_loudness = np.mean(loudness_values)
        std_loudness = np.std(loudness_values)
        
        if mean_loudness == 0:
            return 1.0
            
        # 使用相对变异系数
        cv = std_loudness / abs(mean_loudness)
        return cv
    
    def calculate_improved_snr(self, y):
        """改进的信噪比计算"""
        # 使用更高的频率分辨率
        nperseg = min(4096, len(y) // 4)
        freqs, psd = signal.welch(y, self.sr, nperseg=nperseg)
        
        # 使用更科学的频段划分
        # 语音主要频段: 300-3400Hz
        # 环境噪声通常在更宽的频段
        
        # 计算不同频段的能量
        low_freq_mask = (freqs >= 20) & (freqs < 300)
        speech_freq_mask = (freqs >= 300) & (freqs <= 3400)
        high_freq_mask = (freqs > 3400) & (freqs <= self.sr/2)
        
        if not (np.any(low_freq_mask) and np.any(speech_freq_mask) and np.any(high_freq_mask)):
            return None
            
        low_power = np.mean(psd[low_freq_mask])
        speech_power = np.mean(psd[speech_freq_mask])
        high_power = np.mean(psd[high_freq_mask])
        
        # 估计噪声底噪 (使用最低的频段功率作为参考)
        noise_power = min(low_power, high_power)
        
        if noise_power <= 0:
            return 60  # 很高的SNR
            
        # 计算语音频段的SNR
        snr_db = 10 * np.log10(speech_power / noise_power)
        return snr_db
    
    def calculate_tonality_index(self, y):
        """改进的音调性指数计算"""
        # 使用更精确的音调性检测方法
        
        # 1. 计算自相关函数
        autocorr = np.correlate(y, y, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        
        # 2. 寻找周期性峰值
        # 对应于基频的可能范围 (80-800Hz)
        min_period = int(self.sr / 800)  # 最小周期
        max_period = int(self.sr / 80)   # 最大周期
        
        if max_period >= len(autocorr):
            max_period = len(autocorr) - 1
            
        if min_period >= max_period:
            return 1.0  # 无法检测音调性
            
        # 在可能的基频范围内寻找最大自相关值
        period_range = autocorr[min_period:max_period]
        max_autocorr = np.max(period_range)
        
        # 归一化自相关值
        normalized_autocorr = max_autocorr / autocorr[0] if autocorr[0] > 0 else 0
        
        # 3. 计算频谱平坦度作为补充指标
        stft = librosa.stft(y)
        power_spectrum = np.mean(np.abs(stft)**2, axis=1)
        
        # 几何平均 / 算术平均
        geometric_mean = np.exp(np.mean(np.log(power_spectrum + 1e-10)))
        arithmetic_mean = np.mean(power_spectrum)
        
        spectral_flatness = geometric_mean / arithmetic_mean if arithmetic_mean > 0 else 0
        
        # 结合自相关和频谱平坦度
        # 高自相关 + 低频谱平坦度 = 高音调性
        tonality_index = normalized_autocorr * (1 - spectral_flatness)
        
        return tonality_index
    
    def calculate_bark_scale_spectrum(self, y):
        """计算Bark尺度频谱"""
        stft = librosa.stft(y)
        power_spectrum = np.mean(np.abs(stft)**2, axis=1)
        freqs = librosa.fft_frequencies(sr=self.sr)
        
        # 将功率谱映射到Bark频带
        bark_spectrum = []
        for i in range(len(self.bark_frequencies) - 1):
            freq_mask = (freqs >= self.bark_frequencies[i]) & \
                       (freqs < self.bark_frequencies[i + 1])
            if np.any(freq_mask):
                bark_power = np.mean(power_spectrum[freq_mask])
                bark_spectrum.append(bark_power)
            else:
                bark_spectrum.append(0)
                
        return np.array(bark_spectrum)
    
    def calculate_improved_spectral_slope(self, y):
        """改进的频谱斜率计算"""
        bark_spectrum = self.calculate_bark_scale_spectrum(y)
        
        # 使用Bark频率而不是线性频率
        bark_freqs = self.bark_frequencies[:-1]  # 去掉最后一个边界频率
        
        # 过滤零值
        valid_mask = (bark_spectrum > 0) & (bark_freqs > 0)
        if np.sum(valid_mask) < 3:
            return None, None
            
        valid_freqs = bark_freqs[valid_mask]
        valid_spectrum = bark_spectrum[valid_mask]
        
        # 在log-log尺度上进行线性回归
        log_freqs = np.log10(valid_freqs)
        log_spectrum = np.log10(valid_spectrum)
        
        slope, intercept, r_value, p_value, std_err = linregress(log_freqs, log_spectrum)
        linearity = r_value**2
        
        return slope, linearity
    
    def calculate_roughness(self, y):
        """计算粗糙度 (基于Zwicker模型的简化版本)"""
        # 计算调制频谱
        # 粗糙度主要由15-300Hz的调制引起
        
        # 使用包络检测
        analytic_signal = signal.hilbert(y)
        envelope = np.abs(analytic_signal)
        
        # 计算包络的频谱
        envelope_fft = np.fft.fft(envelope)
        envelope_freqs = np.fft.fftfreq(len(envelope), 1/self.sr)
        envelope_magnitude = np.abs(envelope_fft)
        
        # 粗糙度相关的调制频率范围
        roughness_mask = (envelope_freqs >= 15) & (envelope_freqs <= 300)
        
        if not np.any(roughness_mask):
            return 0
            
        # 计算粗糙度指数 (简化版本)
        roughness_power = np.sum(envelope_magnitude[roughness_mask])
        total_power = np.sum(envelope_magnitude[envelope_freqs >= 0])
        
        roughness_index = roughness_power / total_power if total_power > 0 else 0
        
        return roughness_index
    
    def calculate_sharpness(self, y):
        """计算尖锐度 (基于Zwicker模型的简化版本)"""
        bark_spectrum = self.calculate_bark_scale_spectrum(y)
        
        # 尖锐度权重函数 (简化版本)
        # 高频段有更高的权重
        sharpness_weights = np.linspace(1, 4, len(bark_spectrum))
        
        # 计算加权的重心
        total_loudness = np.sum(bark_spectrum)
        if total_loudness == 0:
            return 0
            
        weighted_sum = np.sum(bark_spectrum * sharpness_weights)
        sharpness = weighted_sum / total_loudness
        
        return sharpness
    
    def comprehensive_analysis(self, y):
        """综合分析"""
        results = {}
        
        # 基础分析
        results['perceptual_loudness_stability'] = self.calculate_perceptual_loudness_stability(y)
        results['improved_snr'] = self.calculate_improved_snr(y)
        results['tonality_index'] = self.calculate_tonality_index(y)
        
        # 频谱分析
        slope, linearity = self.calculate_improved_spectral_slope(y)
        results['spectral_slope'] = slope
        results['spectral_linearity'] = linearity
        
        # 心理声学参数
        results['roughness'] = self.calculate_roughness(y)
        results['sharpness'] = self.calculate_sharpness(y)
        
        # Bark尺度频谱
        results['bark_spectrum'] = self.calculate_bark_scale_spectrum(y)
        
        return results
    
    def evaluate_white_noise_quality(self, results):
        """基于心理声学的白噪音质量评估"""
        score = 100
        issues = []
        
        # 1. 感知响度稳定性 (权重: 30%)
        stability = results.get('perceptual_loudness_stability', 1.0)
        if stability > 0.1:  # 10% 变化阈值
            score -= 30 * min(stability / 0.2, 1.0)  # 最多扣30分
            issues.append(f"感知响度不稳定 ({stability:.1%})")
        
        # 2. 音调性 (权重: 25%)
        tonality = results.get('tonality_index', 0)
        if tonality > 0.3:  # 音调性阈值
            score -= 25 * min(tonality / 0.6, 1.0)
            issues.append(f"包含音调成分 (音调性指数: {tonality:.2f})")
        
        # 3. 频谱平衡 (权重: 20%)
        slope = results.get('spectral_slope')
        if slope is not None:
            # 理想的白噪音斜率应接近0
            slope_deviation = abs(slope)
            if slope_deviation > 0.2:
                score -= 20 * min(slope_deviation / 1.0, 1.0)
                issues.append(f"频谱不平衡 (斜率: {slope:.2f})")
        
        # 4. 粗糙度 (权重: 15%)
        roughness = results.get('roughness', 0)
        if roughness > 0.1:  # 粗糙度阈值
            score -= 15 * min(roughness / 0.3, 1.0)
            issues.append(f"声音粗糙 (粗糙度: {roughness:.2f})")
        
        # 5. 信噪比 (权重: 10%)
        snr = results.get('improved_snr')
        if snr is not None and snr < 25:
            score -= 10 * min((25 - snr) / 15, 1.0)
            issues.append(f"信噪比偏低 ({snr:.1f}dB)")
        
        # 质量等级
        if score >= 85:
            quality = "优秀"
        elif score >= 70:
            quality = "良好"
        elif score >= 50:
            quality = "一般"
        else:
            quality = "不推荐"
            
        return {
            'quality_score': max(0, score),
            'quality_rating': quality,
            'issues': issues,
            'detailed_metrics': results
        }

# 使用示例
def analyze_audio_file_improved(file_path):
    """使用改进算法分析音频文件"""
    try:
        y, sr = librosa.load(file_path, sr=None, mono=True)
        analyzer = ImprovedAudioAnalyzer(sr=sr)
        
        # 执行综合分析
        results = analyzer.comprehensive_analysis(y)
        
        # 评估质量
        evaluation = analyzer.evaluate_white_noise_quality(results)
        
        return evaluation
        
    except Exception as e:
        return {'error': str(e)}

if __name__ == "__main__":
    # 测试示例
    import sys
    if len(sys.argv) > 1:
        result = analyze_audio_file_improved(sys.argv[1])
        print("改进算法分析结果:")
        print(f"质量评级: {result.get('quality_rating', 'N/A')}")
        print(f"质量得分: {result.get('quality_score', 'N/A')}")
        if result.get('issues'):
            print("发现问题:")
            for issue in result['issues']:
                print(f"  - {issue}")
    else:
        print("用法: python improved_audio_analysis.py <audio_file>")
