#!/usr/bin/env python3
"""
智能睡眠音频评估系统 - Noise文件夹专项分析
对 Sounds/Noise 文件夹中的3个标准噪音文件进行详细分析
"""

import os
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import json

# 导入智能睡眠音频评估系统
from smart_sleep_audio_system import SmartSleepAudioSystem, UserGroup

def get_china_timestamp() -> str:
    """获取中国时间戳，格式为 YYYYMMDD_HHMMSS"""
    # 使用本地时间（假设系统时区已设置为中国时区）
    now = datetime.now()
    return now.strftime("%Y%m%d_%H%M%S")

def analyze_noise_files():
    """分析 Sounds/Noise 文件夹中的音频文件"""
    print("🧠 智能睡眠音频评估系统 - Noise文件夹专项分析")
    print("=" * 70)
    
    # 初始化系统
    system = SmartSleepAudioSystem()
    
    # 定义要分析的文件
    noise_folder = Path("Sounds/Noise")
    target_files = [
        "brown-noise.wav",
        "pink-noise.wav", 
        "white-noise.wav"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for filename in target_files:
        file_path = noise_folder / filename
        if file_path.exists():
            existing_files.append(file_path)
            print(f"✅ 找到文件: {filename}")
        else:
            print(f"❌ 文件不存在: {filename}")
    
    if not existing_files:
        print("❌ 没有找到任何目标文件，退出分析")
        return False
    
    print(f"\n📊 开始分析 {len(existing_files)} 个文件...")
    print()
    
    # 存储分析结果
    analysis_results = []
    
    # 分析每个文件
    for i, file_path in enumerate(existing_files, 1):
        print(f"🔍 分析文件 {i}/{len(existing_files)}: {file_path.name}")
        print("-" * 50)
        
        try:
            # 进行完整分析
            report = system.analyze_audio_file(str(file_path))
            
            # 存储结果
            result = {
                'filename': file_path.name,
                'filepath': str(file_path),
                'report': report,
                'analysis_success': True
            }
            analysis_results.append(result)
            
            # 显示基本信息
            print(f"   睡眠适用性得分: {report.sleep_suitability.overall_score:.1f}/100")
            print(f"   噪音类型: {report.audio_features.noise_type.value}")
            print(f"   安全等级: {report.safety_assessment.overall_safety.value}")
            print(f"   效果预测: {report.sleep_suitability.effectiveness_prediction:.1%}")
            print("   ✅ 分析完成")
            
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
            result = {
                'filename': file_path.name,
                'filepath': str(file_path),
                'error': str(e),
                'analysis_success': False
            }
            analysis_results.append(result)
        
        print()
    
    # 生成报告
    if analysis_results:
        timestamp = get_china_timestamp()
        report_filename = f"noise_analysis_report_{timestamp}.md"
        report_path = noise_folder / report_filename
        
        print(f"📝 生成分析报告: {report_filename}")
        success = generate_analysis_report(analysis_results, report_path, timestamp)
        
        if success:
            print(f"✅ 报告已保存到: {report_path}")
            return True
        else:
            print("❌ 报告生成失败")
            return False
    else:
        print("❌ 没有成功的分析结果，无法生成报告")
        return False

def generate_analysis_report(results: List[Dict], report_path: Path, timestamp: str) -> bool:
    """生成详细的分析报告"""
    try:
        # 过滤成功的分析结果
        successful_results = [r for r in results if r.get('analysis_success', False)]
        failed_results = [r for r in results if not r.get('analysis_success', False)]

        # 生成报告内容
        report_content = generate_report_content(successful_results, failed_results, timestamp)

        # 写入文件
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        return True

    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")
        return False

def generate_report_content(successful_results: List[Dict], failed_results: List[Dict], timestamp: str) -> str:
    """生成报告的详细内容"""

    # 获取当前时间的可读格式
    current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")

    report = f"""# 🧠 智能睡眠音频评估系统 - Noise文件夹专项分析报告

## 📋 报告信息

**生成时间**: {current_time} (中国时间)
**时间戳**: {timestamp}
**分析文件数**: {len(successful_results + failed_results)}
**成功分析**: {len(successful_results)}
**分析失败**: {len(failed_results)}
**分析引擎**: 智能睡眠音频评估与推荐系统 v2.0

---

## 📊 分析概览

本报告对 `Sounds/Noise` 文件夹中的标准噪音文件进行了全面的睡眠适用性评估，包括音频特征识别、安全性评估和个性化推荐。

### 🎯 分析目标文件
- **brown-noise.wav** - 棕噪音标准样本
- **pink-noise.wav** - 粉噪音标准样本
- **white-noise.wav** - 白噪音标准样本

---

"""

    if successful_results:
        # 按睡眠适用性得分排序
        sorted_results = sorted(successful_results,
                              key=lambda x: x['report'].sleep_suitability.overall_score,
                              reverse=True)

        # 添加排序结果
        report += "## 🏆 睡眠适用性排序结果\n\n"
        report += "| 排名 | 文件名 | 睡眠得分 | 噪音类型 | 安全等级 | 效果预测 | 推荐状态 |\n"
        report += "|------|--------|----------|----------|----------|----------|----------|\n"

        for i, result in enumerate(sorted_results, 1):
            report_obj = result['report']
            filename = result['filename']
            score = report_obj.sleep_suitability.overall_score
            noise_type = report_obj.audio_features.noise_type.value
            safety = report_obj.safety_assessment.overall_safety.value
            effectiveness = f"{report_obj.sleep_suitability.effectiveness_prediction:.1%}"

            # 根据得分确定推荐状态
            if score >= 70:
                status = "✅ 强烈推荐"
            elif score >= 50:
                status = "⚠️ 可以使用"
            elif score >= 30:
                status = "🤔 谨慎使用"
            else:
                status = "❌ 不推荐"

            report += f"| {i} | **{filename}** | {score:.1f}/100 | {noise_type} | {safety} | {effectiveness} | {status} |\n"

        report += "\n---\n\n"

        # 详细分析每个文件
        report += "## 📁 详细分析结果\n\n"

        for i, result in enumerate(sorted_results, 1):
            report += generate_file_analysis_section(result, i)

        # 对比分析
        report += generate_comparison_analysis(sorted_results)

        # 技术参数汇总
        report += generate_technical_summary(sorted_results)

    else:
        report += "## ❌ 分析结果\n\n没有成功分析的文件。\n\n"

    # 失败的分析
    if failed_results:
        report += "## ⚠️ 分析失败的文件\n\n"
        for result in failed_results:
            report += f"- **{result['filename']}**: {result.get('error', '未知错误')}\n"
        report += "\n"

    # 添加科学依据和建议
    report += generate_scientific_recommendations()

    return report

def generate_file_analysis_section(result: Dict, rank: int) -> str:
    """生成单个文件的详细分析部分"""
    report_obj = result['report']
    filename = result['filename']

    section = f"### {rank}. 📁 {filename}\n\n"

    # 基础信息
    section += f"**📊 睡眠适用性得分**: {report_obj.sleep_suitability.overall_score:.1f}/100\n\n"

    # 音频特征
    section += "#### 🎵 音频特征分析\n\n"
    section += f"- **噪音类型**: {report_obj.audio_features.noise_type.value}\n"
    section += f"- **音频来源**: {report_obj.audio_features.audio_source.value}\n"
    section += f"- **频谱斜率**: {report_obj.audio_features.spectral_slope:.3f}\n"
    section += f"- **响度稳定性**: {report_obj.audio_features.loudness_stability:.3f}\n"
    section += f"- **音调峰值比**: {report_obj.audio_features.tonal_ratio:.2f}\n"
    section += f"- **动态范围**: {report_obj.audio_features.dynamic_range_db:.1f} dB\n"
    section += f"- **声音标签**: {', '.join(report_obj.audio_features.sound_tags)}\n\n"

    # 安全评估
    section += "#### 🛡️ 安全性评估\n\n"
    section += f"- **总体安全等级**: {report_obj.safety_assessment.overall_safety.value}\n"
    section += f"- **音量安全**: {report_obj.safety_assessment.volume_safety.value}\n"
    section += f"- **内容安全**: {report_obj.safety_assessment.content_safety.value}\n"
    section += f"- **推荐音量**: {report_obj.safety_assessment.recommended_volume_db[0]}-{report_obj.safety_assessment.recommended_volume_db[1]} dB\n"
    section += f"- **推荐距离**: {report_obj.safety_assessment.recommended_distance_cm} cm\n"
    section += f"- **最大使用时长**: {report_obj.safety_assessment.max_duration_hours} 小时\n"
    if report_obj.safety_assessment.warnings:
        section += f"- **安全警告**: {'; '.join(report_obj.safety_assessment.warnings)}\n"
    section += "\n"

    # 睡眠适用性
    section += "#### 😴 睡眠适用性评估\n\n"
    section += f"- **效果预测**: {report_obj.sleep_suitability.effectiveness_prediction:.1%}\n"
    section += f"- **干扰风险**: {report_obj.sleep_suitability.disruption_risk:.1%}\n"
    section += f"- **舒适度**: {report_obj.sleep_suitability.comfort_level:.1%}\n"
    section += f"- **科学证据等级**: {report_obj.sleep_suitability.scientific_evidence_level}\n\n"

    # 用户群体推荐
    section += "#### 👥 用户群体推荐\n\n"

    user_groups = [UserGroup.ADULT, UserGroup.INFANT, UserGroup.ELDERLY, UserGroup.INSOMNIA]
    for group in user_groups:
        try:
            # 从报告中获取个性化推荐
            if hasattr(report_obj, 'personalized_recommendations') and group in report_obj.personalized_recommendations:
                personalized = report_obj.personalized_recommendations[group]

                section += f"**{group.value}**:\n"
                section += f"- 推荐得分: {personalized.suitability_score:.1f}/100\n"
                section += f"- 使用建议: {personalized.usage_recommendation}\n"
                section += f"- 主要益处: {', '.join(personalized.benefits)}\n"
                section += f"- 潜在风险: {', '.join(personalized.risks)}\n"
                section += f"- 科学依据: {personalized.scientific_rationale}\n\n"
            else:
                # 生成简化的推荐
                section += generate_simplified_user_recommendation(group, report_obj)
        except Exception as e:
            section += f"**{group.value}**: 推荐数据获取失败 ({str(e)})\n\n"

    section += f"**🎯 总体推荐**: {report_obj.overall_recommendation}\n\n"
    section += "---\n\n"

    return section

def generate_simplified_user_recommendation(user_group: UserGroup, report_obj) -> str:
    """生成简化的用户群体推荐"""
    section = f"**{user_group.value}**:\n"

    # 基于噪音类型和安全等级生成简化推荐
    noise_type = report_obj.audio_features.noise_type.value
    safety_level = report_obj.safety_assessment.overall_safety.value
    score = report_obj.sleep_suitability.overall_score
    effectiveness = report_obj.sleep_suitability.effectiveness_prediction

    # 根据用户群体调整推荐
    if user_group == UserGroup.ADULT:
        if score >= 70 and safety_level == "安全":
            recommendation = "推荐使用：适合作为日常睡眠辅助"
            benefits = ["改善入睡时间", "减少夜间觉醒", "提高睡眠质量"]
            risks = ["长期使用可能产生依赖"]
        elif score >= 50:
            recommendation = "可以使用：有一定效果，注意使用方法"
            benefits = ["一定程度改善睡眠", "屏蔽环境噪声"]
            risks = ["效果因人而异", "需要适应期"]
        else:
            recommendation = "不推荐使用：效果有限"
            benefits = ["可能有轻微放松效果"]
            risks = ["效果不明显", "可能影响睡眠质量"]

    elif user_group == UserGroup.INFANT:
        if noise_type == "绿噪音":
            recommendation = "强烈不推荐：中频集中可能影响听觉发育"
            benefits = []
            risks = ["影响听觉发育", "可能造成听力损伤"]
        elif safety_level == "安全" and score >= 50:
            recommendation = "谨慎使用：音量≤50dB，距离≥2米，仅入睡阶段使用"
            benefits = ["辅助快速入睡", "创造一致的睡眠环境"]
            risks = ["需严格控制音量和距离", "避免长时间连续使用"]
        else:
            recommendation = "不推荐使用：不符合婴幼儿安全标准"
            benefits = []
            risks = ["安全风险", "可能影响睡眠发育"]

    elif user_group == UserGroup.ELDERLY:
        if score >= 60 and safety_level in ["安全", "需要注意"]:
            recommendation = "推荐使用：有助于改善睡眠质量和记忆巩固"
            benefits = ["改善深度睡眠", "增强记忆巩固", "减少夜间觉醒"]
            risks = ["需要适应期", "音量不宜过大"]
        elif score >= 40:
            recommendation = "可以尝试：配合其他睡眠改善措施"
            benefits = ["一定程度改善睡眠", "放松效果"]
            risks = ["效果有限", "需要配合其他方法"]
        else:
            recommendation = "不推荐使用：建议寻找更适合的方法"
            benefits = []
            risks = ["效果不明显", "可能干扰睡眠"]

    elif user_group == UserGroup.INSOMNIA:
        if noise_type == "粉噪音" and score >= 60:
            recommendation = "推荐作为辅助治疗：结合专业治疗使用"
            benefits = ["辅助建立睡眠仪式", "减少入睡时间", "改善睡眠连续性"]
            risks = ["不能替代专业治疗", "需要配合其他方法"]
        elif score >= 50:
            recommendation = "可以尝试：作为辅助手段，但需配合专业治疗"
            benefits = ["一定程度改善睡眠", "建立睡眠仪式"]
            risks = ["效果有限", "不能替代专业治疗"]
        else:
            recommendation = "不推荐使用：建议咨询专业医生"
            benefits = []
            risks = ["效果不明显", "可能延误治疗"]

    # 生成推荐得分（简化计算）
    if user_group == UserGroup.INFANT:
        adjusted_score = max(0, score - 30)  # 婴幼儿安全要求更高
    elif user_group == UserGroup.ELDERLY:
        adjusted_score = min(100, score + 10)  # 老年人可能更受益
    else:
        adjusted_score = score

    section += f"- 推荐得分: {adjusted_score:.1f}/100\n"
    section += f"- 使用建议: {recommendation}\n"
    section += f"- 主要益处: {', '.join(benefits) if benefits else '无明显益处'}\n"
    section += f"- 潜在风险: {', '.join(risks) if risks else '风险较低'}\n"
    section += f"- 科学依据: {noise_type}的{effectiveness:.1%}预期有效性\n\n"

    return section

def generate_comparison_analysis(results: List[Dict]) -> str:
    """生成对比分析部分"""
    section = "## 📈 三种噪音对比分析\n\n"

    if len(results) < 2:
        return section + "分析文件数量不足，无法进行对比分析。\n\n"

    # 科学有效性对比
    section += "### 🔬 科学有效性对比\n\n"
    section += "基于科学研究数据的效果对比：\n\n"

    for result in results:
        report_obj = result['report']
        filename = result['filename']
        noise_type = report_obj.audio_features.noise_type.value
        effectiveness = report_obj.sleep_suitability.effectiveness_prediction

        section += f"- **{filename}** ({noise_type}): {effectiveness:.1%} 预期有效性\n"

    section += "\n"

    # 技术参数对比
    section += "### ⚙️ 技术参数对比\n\n"
    section += "| 文件名 | 频谱斜率 | 响度稳定性 | 动态范围(dB) | 音调峰值比 |\n"
    section += "|--------|----------|------------|--------------|------------|\n"

    for result in results:
        report_obj = result['report']
        filename = result['filename']
        slope = report_obj.audio_features.spectral_slope
        stability = report_obj.audio_features.loudness_stability
        dynamic_range = report_obj.audio_features.dynamic_range_db
        tonal_ratio = report_obj.audio_features.tonal_ratio

        section += f"| {filename} | {slope:.3f} | {stability:.3f} | {dynamic_range:.1f} | {tonal_ratio:.2f} |\n"

    section += "\n"

    # 安全性对比
    section += "### 🛡️ 安全性对比\n\n"
    section += "| 文件名 | 总体安全 | 音量安全 | 内容安全 |\n"
    section += "|--------|----------|----------|----------|\n"

    for result in results:
        report_obj = result['report']
        filename = result['filename']
        overall_safety = report_obj.safety_assessment.overall_safety.value
        volume_safety = report_obj.safety_assessment.volume_safety.value
        content_safety = report_obj.safety_assessment.content_safety.value

        section += f"| {filename} | {overall_safety} | {volume_safety} | {content_safety} |\n"

    section += "\n---\n\n"

    return section

def generate_technical_summary(results: List[Dict]) -> str:
    """生成技术参数汇总"""
    section = "## 📊 技术参数汇总表格\n\n"

    if not results:
        return section + "没有可用的分析结果。\n\n"

    # 详细技术参数表格
    section += "### 🔧 详细技术参数\n\n"
    section += "| 参数 | brown-noise.wav | pink-noise.wav | white-noise.wav |\n"
    section += "|------|-----------------|----------------|------------------|\n"

    # 创建参数字典，方便查找
    param_dict = {}
    for result in results:
        filename = result['filename']
        report_obj = result['report']
        param_dict[filename] = report_obj

    # 定义要显示的参数
    params = [
        ("睡眠适用性得分", lambda r: f"{r.sleep_suitability.overall_score:.1f}/100"),
        ("噪音类型", lambda r: r.audio_features.noise_type.value),
        ("频谱斜率", lambda r: f"{r.audio_features.spectral_slope:.3f}"),
        ("响度稳定性", lambda r: f"{r.audio_features.loudness_stability:.3f}"),
        ("动态范围(dB)", lambda r: f"{r.audio_features.dynamic_range_db:.1f}"),
        ("音调峰值比", lambda r: f"{r.audio_features.tonal_ratio:.2f}"),
        ("效果预测", lambda r: f"{r.sleep_suitability.effectiveness_prediction:.1%}"),
        ("干扰风险", lambda r: f"{r.sleep_suitability.disturbance_risk:.1%}"),
        ("舒适度", lambda r: f"{r.sleep_suitability.comfort_level:.1%}"),
        ("总体安全等级", lambda r: r.safety_assessment.overall_safety.value),
        ("科学证据等级", lambda r: r.sleep_suitability.scientific_evidence_level)
    ]

    target_files = ["brown-noise.wav", "pink-noise.wav", "white-noise.wav"]

    for param_name, param_func in params:
        row = f"| {param_name} |"
        for filename in target_files:
            if filename in param_dict:
                try:
                    value = param_func(param_dict[filename])
                    row += f" {value} |"
                except:
                    row += " N/A |"
            else:
                row += " 未分析 |"
        section += row + "\n"

    section += "\n"

    # 性能统计
    section += "### 📈 性能统计\n\n"
    if results:
        scores = [r['report'].sleep_suitability.overall_score for r in results]
        avg_score = sum(scores) / len(scores)
        max_score = max(scores)
        min_score = min(scores)

        section += f"- **平均睡眠适用性得分**: {avg_score:.1f}/100\n"
        section += f"- **最高得分**: {max_score:.1f}/100\n"
        section += f"- **最低得分**: {min_score:.1f}/100\n"
        section += f"- **得分差异**: {max_score - min_score:.1f}分\n\n"

    section += "---\n\n"

    return section

def generate_scientific_recommendations() -> str:
    """生成科学依据和推荐建议"""
    section = "## 🔬 科学依据与推荐建议\n\n"

    section += "### 📚 科学研究依据\n\n"
    section += "本分析基于以下科学研究数据：\n\n"
    section += "1. **粉噪音效果**: 82%的研究显示粉噪音对睡眠有积极影响\n"
    section += "2. **白噪音效果**: 33%的研究显示白噪音对睡眠有积极影响\n"
    section += "3. **棕噪音效果**: 基于低频偏好理论，估计65%有效性\n"
    section += "4. **心理声学原理**: 采用A-weighting和Bark尺度分析\n"
    section += "5. **安全标准**: 基于WHO和相关医学研究的音量安全阈值\n\n"

    section += "### 🎯 使用建议\n\n"
    section += "#### 💡 一般使用原则\n\n"
    section += "1. **音量控制**: 成人≤60dB，婴幼儿≤50dB，距离≥2米\n"
    section += "2. **使用时间**: 建议睡前30分钟开始播放，入睡后可继续\n"
    section += "3. **环境配置**: 在安静的卧室环境中使用效果最佳\n"
    section += "4. **个体差异**: 根据个人偏好和反应调整使用方式\n\n"

    section += "#### 🚨 安全注意事项\n\n"
    section += "1. **婴幼儿使用**: 严格控制音量和距离，避免长时间连续使用\n"
    section += "2. **听力保护**: 定期检查听力，如有不适立即停止使用\n"
    section += "3. **依赖性**: 避免过度依赖，建议间歇性使用\n"
    section += "4. **医疗咨询**: 有听力问题或睡眠障碍者请咨询医生\n\n"

    section += "### 📊 推荐优先级\n\n"
    section += "基于科学数据和分析结果，推荐使用优先级：\n\n"
    section += "1. **粉噪音** - 科学证据最强，82%研究显示有效\n"
    section += "2. **棕噪音** - 低频丰富，理论支持较好\n"
    section += "3. **白噪音** - 传统选择，但科学支持相对较弱\n\n"

    section += "---\n\n"
    section += "**报告生成**: 智能睡眠音频评估与推荐系统 v2.0  \n"
    section += "**技术支持**: 基于librosa、scipy和numpy的专业音频分析  \n"
    section += "**数据来源**: 《白噪音对睡眠影响的科学分析报告》及相关研究文献\n\n"

    return section

if __name__ == "__main__":
    success = analyze_noise_files()
    if success:
        print("\n🎉 分析完成！")
    else:
        print("\n❌ 分析失败！")
        sys.exit(1)
