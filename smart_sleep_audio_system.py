"""
智能睡眠音频评估与推荐系统
基于科学研究的个性化睡眠音频分析和推荐引擎
"""

import numpy as np
import librosa
from scipy import signal
from scipy.stats import linregress
import json
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# 导入现有的分析工具
from noise_analyzer import (
    apply_a_weighting, calculate_bark_spectrum, calculate_bark_spectral_balance,
    calculate_snr, calculate_dynamic_range, detect_clipping, 
    detect_spectral_distortion, assess_background_noise
)

class NoiseType(Enum):
    """噪音类型枚举"""
    WHITE = "白噪音"
    PINK = "粉噪音"
    BROWN = "棕噪音"
    GREEN = "绿噪音"
    DEEP_RED = "深红噪音"
    COMPLEX = "复杂噪音"

class AudioSource(Enum):
    """音频来源类型"""
    NATURAL = "自然声音"
    MECHANICAL = "机械噪音"
    SYNTHETIC = "合成噪音"
    MIXED = "混合声音"

class UserGroup(Enum):
    """用户群体类型"""
    ADULT = "成人"
    INFANT = "婴幼儿"
    ELDERLY = "老年人"
    INSOMNIA = "失眠患者"

class SafetyLevel(Enum):
    """安全等级"""
    SAFE = "安全"
    CAUTION = "需要注意"
    WARNING = "警告"
    DANGEROUS = "危险"

@dataclass
class AudioFeatures:
    """音频特征数据类"""
    # 基础技术参数
    spectral_slope: float
    spectral_linearity: float
    loudness_stability: float
    tonal_ratio: float
    snr_db: Optional[float]
    dynamic_range_db: Optional[float]
    clipping_percentage: Optional[float]
    spectral_distortion: Optional[float]
    noise_level_db: Optional[float]
    bark_spectral_balance: Optional[float]
    bark_bands_count: int
    
    # 音频基本信息
    duration_seconds: float
    sample_rate: int
    rms_level_db: float
    
    # 分类信息
    noise_type: NoiseType
    audio_source: AudioSource
    sound_tags: List[str]

@dataclass
class SafetyAssessment:
    """安全评估结果"""
    overall_safety: SafetyLevel
    volume_safety: SafetyLevel
    duration_safety: SafetyLevel
    content_safety: SafetyLevel
    recommended_volume_db: Tuple[int, int]
    recommended_distance_cm: int
    max_duration_hours: Optional[float]
    warnings: List[str]
    safety_score: float

@dataclass
class SleepSuitability:
    """睡眠适用性评估"""
    overall_score: float
    effectiveness_prediction: float  # 基于科学研究的效果预测
    sleep_phase_suitability: Dict[str, float]  # 不同睡眠阶段的适用性
    disruption_risk: float
    comfort_level: float
    scientific_evidence_level: str
    
@dataclass
class PersonalizedRecommendation:
    """个性化推荐结果"""
    user_group: UserGroup
    suitability_score: float
    usage_recommendation: str
    optimal_settings: Dict[str, Union[str, int, float]]
    benefits: List[str]
    risks: List[str]
    alternatives: List[str]
    scientific_rationale: str

@dataclass
class ComprehensiveReport:
    """综合评估报告"""
    audio_file: str
    analysis_timestamp: str
    audio_features: AudioFeatures
    safety_assessment: SafetyAssessment
    sleep_suitability: SleepSuitability
    personalized_recommendations: Dict[UserGroup, PersonalizedRecommendation]
    overall_recommendation: str
    scientific_references: List[str]

class SmartSleepAudioSystem:
    """智能睡眠音频评估与推荐系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.scientific_data = self._load_scientific_data()
        self.sound_pattern_database = self._initialize_sound_patterns()
        
    def _load_scientific_data(self) -> Dict:
        """加载科学研究数据"""
        return {
            'noise_effectiveness': {
                NoiseType.PINK: 0.82,      # 82%的研究显示有效
                NoiseType.WHITE: 0.33,     # 33%的研究显示有效
                NoiseType.BROWN: 0.65,     # 估计值，基于低频偏好
                NoiseType.GREEN: 0.55,     # 估计值，介于白噪音和粉噪音之间
                NoiseType.DEEP_RED: 0.45   # 估计值
            },
            'safety_thresholds': {
                UserGroup.ADULT: {'max_db': 60, 'min_distance_cm': 30},
                UserGroup.INFANT: {'max_db': 50, 'min_distance_cm': 200},
                UserGroup.ELDERLY: {'max_db': 55, 'min_distance_cm': 50},
                UserGroup.INSOMNIA: {'max_db': 60, 'min_distance_cm': 30}
            },
            'green_noise_safety': {
                UserGroup.ADULT: {
                    'max_db': 60,
                    'min_distance_cm': 30,
                    'max_duration_hours': 8,
                    'special_considerations': ['避免过度依赖', '注意中频敏感性']
                },
                UserGroup.INFANT: {
                    'max_db': 45,
                    'min_distance_cm': 200,
                    'max_duration_hours': 2,
                    'special_considerations': ['中频可能影响听觉发育', '严格限制使用时长', '仅入睡阶段使用']
                },
                UserGroup.ELDERLY: {
                    'max_db': 55,
                    'min_distance_cm': 50,
                    'max_duration_hours': 6,
                    'special_considerations': ['中频丰富有助听觉刺激', '注意听力保护']
                },
                UserGroup.INSOMNIA: {
                    'max_db': 58,
                    'min_distance_cm': 30,
                    'max_duration_hours': 8,
                    'special_considerations': ['作为粉噪音替代选择', '建立睡眠仪式感']
                }
            },
            'optimal_parameters': {
                'spectral_slope_ranges': {
                    NoiseType.WHITE: (-0.2, 0.2),
                    NoiseType.PINK: (-1.2, -0.8),
                    NoiseType.BROWN: (-2.2, -1.8)
                },
                'max_dynamic_range_db': 10,
                'max_tonal_ratio': 5,
                'min_stability_percent': 95
            }
        }
    
    def _initialize_sound_patterns(self) -> Dict:
        """初始化声音模式识别数据库"""
        return {
            'natural_water': {
                'keywords': ['water', 'wave', 'ocean', 'sea', 'rain', 'stream', 'river'],
                'spectral_characteristics': {'low_freq_dominant': True, 'periodic': True},
                'sleep_benefit': 0.8
            },
            'natural_wind': {
                'keywords': ['wind', 'breeze', 'air'],
                'spectral_characteristics': {'broadband': True, 'continuous': True},
                'sleep_benefit': 0.6
            },
            'natural_forest': {
                'keywords': ['forest', 'tree', 'leaves', 'birds'],
                'spectral_characteristics': {'complex': True, 'tonal_elements': True},
                'sleep_benefit': 0.3
            },
            'mechanical_fan': {
                'keywords': ['fan', 'motor', 'hum'],
                'spectral_characteristics': {'monotone': True, 'stable': True},
                'sleep_benefit': 0.5
            },
            'weather_storm': {
                'keywords': ['storm', 'thunder', 'lightning'],
                'spectral_characteristics': {'sudden_events': True, 'high_dynamic': True},
                'sleep_benefit': 0.1
            }
        }
    
    def analyze_audio_file(self, file_path: str) -> ComprehensiveReport:
        """分析音频文件并生成综合报告"""
        try:
            # 加载音频
            y, sr = librosa.load(file_path, sr=None, mono=True)
            
            # 1. 音频特征识别
            audio_features = self._extract_audio_features(y, sr, file_path)
            
            # 2. 安全评估
            safety_assessment = self._assess_safety(audio_features)
            
            # 3. 睡眠适用性评估
            sleep_suitability = self._evaluate_sleep_suitability(audio_features)
            
            # 4. 个性化推荐
            personalized_recommendations = self._generate_personalized_recommendations(
                audio_features, safety_assessment, sleep_suitability
            )
            
            # 5. 生成综合报告
            report = ComprehensiveReport(
                audio_file=file_path,
                analysis_timestamp=datetime.now().isoformat(),
                audio_features=audio_features,
                safety_assessment=safety_assessment,
                sleep_suitability=sleep_suitability,
                personalized_recommendations=personalized_recommendations,
                overall_recommendation=self._generate_overall_recommendation(
                    sleep_suitability, safety_assessment
                ),
                scientific_references=self._get_scientific_references()
            )
            
            return report
            
        except Exception as e:
            raise Exception(f"音频分析失败: {str(e)}")
    
    def _extract_audio_features(self, y: np.ndarray, sr: int, file_path: str) -> AudioFeatures:
        """提取音频特征"""
        # 计算基础技术参数
        spectral_slope, spectral_linearity = self._calculate_spectral_slope(y, sr)
        loudness_stability = self._calculate_loudness_stability(y, sr)
        tonal_ratio = self._calculate_tonal_ratio(y, sr)
        
        # 高级分析
        snr_db = calculate_snr(y, sr)
        dynamic_range_db = calculate_dynamic_range(y)
        clipping_percentage = detect_clipping(y)
        spectral_distortion = detect_spectral_distortion(y, sr)
        noise_level_db = assess_background_noise(y, sr)
        
        # Bark尺度分析
        bark_spectrum, _ = calculate_bark_spectrum(y, sr)
        bark_spectral_balance = calculate_bark_spectral_balance(bark_spectrum)
        bark_bands_count = len(bark_spectrum) if bark_spectrum is not None else 0
        
        # 音频基本信息
        duration_seconds = len(y) / sr
        rms_level = np.sqrt(np.mean(y**2))
        rms_level_db = 20 * np.log10(rms_level + 1e-10)
        
        # 分类识别
        noise_type = self._classify_noise_type(spectral_slope, y, sr)
        audio_source = self._identify_audio_source(y, sr, file_path)
        sound_tags = self._extract_sound_tags(file_path, y, sr)
        
        return AudioFeatures(
            spectral_slope=spectral_slope,
            spectral_linearity=spectral_linearity,
            loudness_stability=loudness_stability,
            tonal_ratio=tonal_ratio,
            snr_db=snr_db,
            dynamic_range_db=dynamic_range_db,
            clipping_percentage=clipping_percentage,
            spectral_distortion=spectral_distortion,
            noise_level_db=noise_level_db,
            bark_spectral_balance=bark_spectral_balance,
            bark_bands_count=bark_bands_count,
            duration_seconds=duration_seconds,
            sample_rate=sr,
            rms_level_db=rms_level_db,
            noise_type=noise_type,
            audio_source=audio_source,
            sound_tags=sound_tags
        )
    
    def _calculate_spectral_slope(self, y: np.ndarray, sr: int) -> Tuple[float, float]:
        """计算频谱斜率和线性度"""
        try:
            # 使用Bark尺度分析
            bark_spectrum, bark_centers = calculate_bark_spectrum(y, sr)
            
            if bark_spectrum is not None and bark_centers is not None and len(bark_spectrum) >= 3:
                valid_mask = (bark_spectrum > 0) & (bark_centers > 0)
                if np.sum(valid_mask) >= 3:
                    valid_freqs = bark_centers[valid_mask]
                    valid_spectrum = bark_spectrum[valid_mask]
                    
                    log_freqs = np.log(valid_freqs)
                    log_spectrum = np.log(valid_spectrum)
                    
                    slope, _, r_value, _, _ = linregress(log_freqs, log_spectrum)
                    linearity = r_value**2
                    
                    return slope, linearity
            
            # 回退到传统方法
            stft_result = librosa.stft(y)
            power_spectrum = np.abs(stft_result)**2
            avg_power_spectrum = np.mean(power_spectrum, axis=1)
            freqs = librosa.fft_frequencies(sr=sr)
            
            valid_indices = np.where((freqs > 0) & (avg_power_spectrum > 0))
            if len(valid_indices[0]) < 2:
                return 0, 0
                
            log_freqs = np.log(freqs[valid_indices])
            log_power = np.log(avg_power_spectrum[valid_indices])
            
            slope, _, r_value, _, _ = linregress(log_freqs, log_power)
            linearity = r_value**2
            
            return slope, linearity
            
        except Exception:
            return 0, 0
    
    def _calculate_loudness_stability(self, y: np.ndarray, sr: int) -> float:
        """计算A-weighting响度稳定性"""
        try:
            # 应用A-weighting
            y_weighted = apply_a_weighting(y, sr)
            
            # 计算RMS能量
            frame_length = int(0.025 * sr)  # 25ms
            hop_length = int(0.01 * sr)     # 10ms
            rms = librosa.feature.rms(y=y_weighted, frame_length=frame_length, hop_length=hop_length)[0]
            
            if len(rms) == 0 or np.mean(rms) == 0:
                return 1.0
                
            # 过滤过小值
            rms_filtered = rms[rms > np.max(rms) * 0.01]
            
            if len(rms_filtered) < 2:
                return 1.0
                
            # 计算变异系数
            mean_rms = np.mean(rms_filtered)
            std_rms = np.std(rms_filtered)
            
            if mean_rms == 0:
                return 1.0
                
            cv = std_rms / mean_rms
            return cv
            
        except Exception:
            return 1.0
    
    def _calculate_tonal_ratio(self, y: np.ndarray, sr: int) -> float:
        """计算音调峰值比"""
        try:
            stft_result = librosa.stft(y)
            avg_power_spectrum = np.mean(np.abs(stft_result)**2, axis=1)

            if np.median(avg_power_spectrum) == 0:
                return 1.0

            peak_to_median_ratio = np.max(avg_power_spectrum) / np.median(avg_power_spectrum)
            return peak_to_median_ratio

        except Exception:
            return 1.0

    def _detect_green_noise(self, bark_spectrum: np.ndarray, bark_centers: np.ndarray) -> Tuple[bool, float]:
        """
        检测绿噪音特征
        绿噪音特征：中频（400-800Hz）能量集中，呈现山峰状频谱

        Args:
            bark_spectrum: Bark尺度频谱
            bark_centers: Bark频带中心频率

        Returns:
            Tuple[bool, float]: (是否为绿噪音, 置信度分数0-1)
        """
        try:
            if bark_spectrum is None or bark_centers is None or len(bark_spectrum) < 10:
                return False, 0.0

            # 查找中频峰值（约500Hz对应的Bark频带）
            mid_freq_bands = (bark_centers >= 400) & (bark_centers <= 800)
            if not np.any(mid_freq_bands):
                return False, 0.0

            # 计算不同频段的能量
            mid_energy = np.mean(bark_spectrum[mid_freq_bands])
            low_energy = np.mean(bark_spectrum[:len(bark_spectrum)//3])
            high_energy = np.mean(bark_spectrum[2*len(bark_spectrum)//3:])
            total_energy = np.mean(bark_spectrum)

            # 绿噪音特征：中频能量明显高于低频和高频
            if low_energy + high_energy == 0:
                return False, 0.0

            mid_dominance = mid_energy / (low_energy + high_energy + 1e-10)

            # 检查中频是否高于平均水平
            mid_vs_average = mid_energy / (total_energy + 1e-10)

            # 判断是否为绿噪音
            # 条件1: 中频能量明显高于低频和高频的平均值
            # 条件2: 中频能量高于整体平均水平
            is_green = (mid_dominance > 1.5) and (mid_vs_average > 1.2)

            # 计算置信度分数
            confidence = min(mid_dominance / 3.0, 1.0) * min(mid_vs_average / 2.0, 1.0)
            confidence = max(0.0, min(1.0, confidence))

            return is_green, confidence

        except Exception:
            return False, 0.0

    def _evaluate_green_noise_quality(self, features: AudioFeatures, bark_spectrum: np.ndarray, bark_centers: np.ndarray) -> Tuple[float, List[str]]:
        """
        绿噪音专用质量评估算法

        Args:
            features: 音频特征数据
            bark_spectrum: Bark尺度频谱
            bark_centers: Bark频带中心频率

        Returns:
            Tuple[float, List[str]]: (质量评分0-100, 问题列表)
        """
        try:
            score = 100
            issues = []

            if bark_spectrum is None or bark_centers is None or len(bark_spectrum) < 10:
                return 0, ["无法获取有效的频谱数据"]

            # 1. 中频集中度评估 (权重: 30%)
            mid_freq_concentration = self._calculate_mid_freq_concentration(bark_spectrum, bark_centers)
            if mid_freq_concentration < 0.3:
                score -= 30
                issues.append("中频能量集中度不足，不符合绿噪音特征")
            elif mid_freq_concentration < 0.5:
                score -= 15
                issues.append("中频能量集中度偏低")

            # 2. 频谱平衡性评估 (权重: 25%)
            spectral_balance = self._calculate_green_noise_balance(bark_spectrum, bark_centers)
            if spectral_balance < 0.4:
                score -= 25
                issues.append("频谱平衡性不佳，缺乏山峰状特征")
            elif spectral_balance < 0.6:
                score -= 12
                issues.append("频谱平衡性一般")

            # 3. 自然度评估 (权重: 20%)
            naturalness_score = self._assess_green_noise_naturalness(features)
            if naturalness_score < 0.3:
                score -= 20
                issues.append("缺乏自然感，听感可能不佳")
            elif naturalness_score < 0.5:
                score -= 10
                issues.append("自然度一般")

            # 4. 稳定性评估 (权重: 15%)
            if features.loudness_stability > 0.15:
                score -= 15
                issues.append("响度不够稳定，可能影响睡眠")
            elif features.loudness_stability > 0.1:
                score -= 8
                issues.append("响度稳定性一般")

            # 5. 音调纯度评估 (权重: 10%)
            if features.tonal_ratio > 100:
                score -= 10
                issues.append("包含过多音调成分，可能干扰睡眠")
            elif features.tonal_ratio > 50:
                score -= 5
                issues.append("音调成分偏高")

            # 额外的绿噪音特异性检查
            if features.noise_type != NoiseType.GREEN:
                score -= 20
                issues.append("未被识别为绿噪音，可能不符合标准")

            return max(0, score), issues

        except Exception as e:
            return 0, [f"质量评估失败: {str(e)}"]

    def _calculate_mid_freq_concentration(self, bark_spectrum: np.ndarray, bark_centers: np.ndarray) -> float:
        """计算中频集中度"""
        try:
            # 定义中频范围 (400-800Hz)
            mid_freq_mask = (bark_centers >= 400) & (bark_centers <= 800)
            if not np.any(mid_freq_mask):
                return 0.0

            mid_energy = np.sum(bark_spectrum[mid_freq_mask])
            total_energy = np.sum(bark_spectrum)

            if total_energy == 0:
                return 0.0

            concentration = mid_energy / total_energy
            return min(1.0, concentration * 2.0)  # 归一化到0-1范围

        except Exception:
            return 0.0

    def _calculate_green_noise_balance(self, bark_spectrum: np.ndarray, bark_centers: np.ndarray) -> float:
        """计算绿噪音频谱平衡性（山峰状特征）"""
        try:
            if len(bark_spectrum) < 5:
                return 0.0

            # 找到中频峰值位置
            mid_freq_mask = (bark_centers >= 400) & (bark_centers <= 800)
            if not np.any(mid_freq_mask):
                return 0.0

            mid_indices = np.where(mid_freq_mask)[0]
            peak_index = mid_indices[np.argmax(bark_spectrum[mid_indices])]
            peak_value = bark_spectrum[peak_index]

            if peak_value == 0:
                return 0.0

            # 计算左右两侧的衰减情况
            left_decay = 0.0
            right_decay = 0.0

            # 左侧衰减
            if peak_index > 2:
                left_values = bark_spectrum[:peak_index]
                if len(left_values) > 0:
                    left_decay = 1.0 - (np.mean(left_values) / peak_value)

            # 右侧衰减
            if peak_index < len(bark_spectrum) - 3:
                right_values = bark_spectrum[peak_index+1:]
                if len(right_values) > 0:
                    right_decay = 1.0 - (np.mean(right_values) / peak_value)

            # 平衡性评分：两侧衰减的平均值
            balance = (left_decay + right_decay) / 2.0
            return max(0.0, min(1.0, balance))

        except Exception:
            return 0.0

    def _assess_green_noise_naturalness(self, features: AudioFeatures) -> float:
        """评估绿噪音的自然度"""
        try:
            naturalness = 0.5  # 基础分数

            # 音频来源加分
            if features.audio_source == AudioSource.NATURAL:
                naturalness += 0.3
            elif features.audio_source == AudioSource.MIXED:
                naturalness += 0.1
            elif features.audio_source == AudioSource.MECHANICAL:
                naturalness -= 0.2

            # 声音标签加分
            natural_tags = ["Natural", "Forest", "Water", "Wind", "Rain"]
            for tag in features.sound_tags:
                if any(natural_word in tag for natural_word in natural_tags):
                    naturalness += 0.1
                    break

            # 动态范围适中加分
            if features.dynamic_range_db and 5 <= features.dynamic_range_db <= 20:
                naturalness += 0.1
            elif features.dynamic_range_db and features.dynamic_range_db > 30:
                naturalness -= 0.2

            # 频谱复杂度适中加分
            if features.bark_spectral_balance and 0.1 <= features.bark_spectral_balance <= 0.3:
                naturalness += 0.1

            return max(0.0, min(1.0, naturalness))

        except Exception:
            return 0.5

    def _classify_noise_type(self, spectral_slope: float, y: np.ndarray = None, sr: int = None) -> NoiseType:
        """
        基于频谱斜率和Bark尺度分析分类噪音类型
        优先检测绿噪音，然后使用传统的频谱斜率分类
        """
        # 如果提供了音频数据，先检测绿噪音
        if y is not None and sr is not None:
            try:
                bark_spectrum, bark_centers = calculate_bark_spectrum(y, sr)
                if bark_spectrum is not None and bark_centers is not None:
                    is_green, confidence = self._detect_green_noise(bark_spectrum, bark_centers)
                    if is_green and confidence > 0.6:  # 高置信度才认定为绿噪音
                        return NoiseType.GREEN
            except Exception:
                pass  # 如果绿噪音检测失败，继续使用传统方法

        # 传统的基于频谱斜率的分类
        if -0.2 <= spectral_slope <= 0.2:
            return NoiseType.WHITE
        elif -1.2 <= spectral_slope <= -0.8:
            return NoiseType.PINK
        elif -2.2 <= spectral_slope <= -1.8:
            return NoiseType.BROWN
        elif spectral_slope < -2.5:
            return NoiseType.DEEP_RED
        else:
            return NoiseType.COMPLEX

    def _identify_audio_source(self, y: np.ndarray, sr: int, file_path: str) -> AudioSource:
        """识别音频来源类型"""
        filename = file_path.lower()

        # 基于文件名的初步判断
        natural_keywords = ['water', 'wave', 'ocean', 'rain', 'wind', 'forest', 'bird', 'nature']
        mechanical_keywords = ['fan', 'motor', 'machine', 'hum', 'buzz']

        if any(keyword in filename for keyword in natural_keywords):
            return AudioSource.NATURAL
        elif any(keyword in filename for keyword in mechanical_keywords):
            return AudioSource.MECHANICAL

        # 基于音频特征的判断
        # 计算频谱复杂度
        stft = librosa.stft(y)
        spectral_complexity = np.std(np.mean(np.abs(stft), axis=1))

        # 计算时域变化
        rms = librosa.feature.rms(y=y)[0]
        temporal_variation = np.std(rms) / (np.mean(rms) + 1e-10)

        if spectral_complexity > 0.1 and temporal_variation > 0.2:
            return AudioSource.NATURAL
        elif spectral_complexity < 0.05 and temporal_variation < 0.1:
            return AudioSource.MECHANICAL
        else:
            return AudioSource.MIXED

    def _extract_sound_tags(self, file_path: str, y: np.ndarray, sr: int) -> List[str]:
        """提取声音特征标签"""
        tags = []
        filename = file_path.lower()

        # 基于文件名的标签提取
        for pattern_name, pattern_data in self.sound_pattern_database.items():
            if any(keyword in filename for keyword in pattern_data['keywords']):
                tags.append(pattern_name.replace('_', ' ').title())

        # 基于音频特征的标签
        # 检测周期性（如海浪）- 优化版本，避免长时间计算
        if len(y) > sr * 30:  # 如果音频超过30秒，只取前30秒进行分析
            y_sample = y[:sr * 30]
        else:
            y_sample = y

        try:
            # 使用更高效的自相关计算
            if len(y_sample) > sr * 2:  # 至少2秒的数据
                # 降采样以提高计算效率
                downsample_factor = max(1, len(y_sample) // (sr * 10))
                y_downsampled = y_sample[::downsample_factor]

                autocorr = np.correlate(y_downsampled, y_downsampled, mode='full')
                autocorr = autocorr[len(autocorr)//2:]

                if len(autocorr) > len(y_downsampled) // 10:
                    # 寻找周期性模式
                    period_range = autocorr[len(y_downsampled)//10:len(y_downsampled)//2]
                    if len(period_range) > 0 and np.max(period_range) > 0.3 * autocorr[0]:
                        tags.append("Rhythmic Pattern")
        except Exception:
            # 如果自相关计算失败，跳过周期性检测
            pass

        # 检测突发事件
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        if len(rms) > 0:
            rms_threshold = np.mean(rms) + 2 * np.std(rms)
            sudden_events = np.sum(rms > rms_threshold)
            if sudden_events > len(rms) * 0.05:  # 超过5%的帧有突发事件
                tags.append("Sudden Events")

        # 检测连续性
        zero_crossings = librosa.feature.zero_crossing_rate(y)[0]
        if np.mean(zero_crossings) > 0.1:
            tags.append("High Frequency Content")
        elif np.mean(zero_crossings) < 0.05:
            tags.append("Low Frequency Dominant")

        return tags if tags else ["Unclassified"]

    def _assess_safety(self, features: AudioFeatures) -> SafetyAssessment:
        """评估音频安全性"""
        warnings = []

        # 音量安全评估
        estimated_playback_db = features.rms_level_db + 70  # 估算播放音量

        if estimated_playback_db > 85:
            volume_safety = SafetyLevel.DANGEROUS
            warnings.append("音量过高，可能损害听力")
        elif estimated_playback_db > 70:
            volume_safety = SafetyLevel.WARNING
            warnings.append("音量较高，建议降低播放音量")
        elif estimated_playback_db > 60:
            volume_safety = SafetyLevel.CAUTION
            warnings.append("音量适中，注意使用时长")
        else:
            volume_safety = SafetyLevel.SAFE

        # 时长安全评估
        if features.duration_seconds > 8 * 3600:  # 超过8小时
            duration_safety = SafetyLevel.WARNING
            warnings.append("音频过长，建议分段使用")
        elif features.duration_seconds > 4 * 3600:  # 超过4小时
            duration_safety = SafetyLevel.CAUTION
        else:
            duration_safety = SafetyLevel.SAFE

        # 内容安全评估
        content_safety = SafetyLevel.SAFE

        # 检测削波
        if features.clipping_percentage and features.clipping_percentage > 1.0:
            content_safety = SafetyLevel.WARNING
            warnings.append("音频存在削波失真")

        # 检测突发声音
        if features.dynamic_range_db and features.dynamic_range_db > 40:
            content_safety = SafetyLevel.WARNING
            warnings.append("动态范围过大，可能有突发声音")

        # 检测高音调内容
        if features.tonal_ratio > 1000:
            content_safety = SafetyLevel.CAUTION
            warnings.append("包含明显音调成分，可能影响睡眠")

        # 绿噪音特有的风险检查
        if features.noise_type == NoiseType.GREEN:
            warnings.append("⚠️ 实验性功能：绿噪音缺乏充分的科学验证，使用时请注意个体反应")
            # 对于特定用户群体的额外警告
            if hasattr(self, '_current_user_group'):
                if self._current_user_group == UserGroup.INFANT:
                    content_safety = SafetyLevel.WARNING
                    warnings.append("🚨 不推荐婴幼儿使用：中频集中可能影响听觉发育")

        # 综合安全评估
        safety_levels = [volume_safety, duration_safety, content_safety]
        if SafetyLevel.DANGEROUS in safety_levels:
            overall_safety = SafetyLevel.DANGEROUS
        elif SafetyLevel.WARNING in safety_levels:
            overall_safety = SafetyLevel.WARNING
        elif SafetyLevel.CAUTION in safety_levels:
            overall_safety = SafetyLevel.CAUTION
        else:
            overall_safety = SafetyLevel.SAFE

        # 计算安全得分
        safety_score = 100
        if volume_safety == SafetyLevel.DANGEROUS:
            safety_score -= 50
        elif volume_safety == SafetyLevel.WARNING:
            safety_score -= 30
        elif volume_safety == SafetyLevel.CAUTION:
            safety_score -= 15

        if content_safety == SafetyLevel.WARNING:
            safety_score -= 20
        elif content_safety == SafetyLevel.CAUTION:
            safety_score -= 10

        return SafetyAssessment(
            overall_safety=overall_safety,
            volume_safety=volume_safety,
            duration_safety=duration_safety,
            content_safety=content_safety,
            recommended_volume_db=(45, 60),
            recommended_distance_cm=50,
            max_duration_hours=8.0,
            warnings=warnings,
            safety_score=max(0, safety_score)
        )

    def _evaluate_sleep_suitability(self, features: AudioFeatures) -> SleepSuitability:
        """评估睡眠适用性"""
        # 基于科学研究的效果预测
        noise_effectiveness = self.scientific_data['noise_effectiveness'].get(
            features.noise_type, 0.3
        )

        # 计算各项睡眠质量因子
        stability_factor = max(0, 1 - features.loudness_stability)  # 稳定性越高越好
        tonal_factor = max(0, 1 - min(features.tonal_ratio / 100, 1))  # 音调性越低越好

        dynamic_factor = 1.0
        if features.dynamic_range_db:
            dynamic_factor = max(0, 1 - max(0, features.dynamic_range_db - 10) / 30)

        # 频谱适合度（粉噪音最佳）
        spectrum_factor = 0.5  # 默认值
        if features.noise_type == NoiseType.PINK:
            spectrum_factor = 1.0
        elif features.noise_type == NoiseType.WHITE:
            spectrum_factor = 0.4
        elif features.noise_type == NoiseType.BROWN:
            spectrum_factor = 0.8
        elif features.noise_type == NoiseType.GREEN:
            spectrum_factor = 0.7  # 绿噪音：介于粉噪音和棕噪音之间
        elif features.noise_type == NoiseType.DEEP_RED:
            spectrum_factor = 0.6

        # 自然声音加分
        source_factor = 1.0
        if features.audio_source == AudioSource.NATURAL:
            source_factor = 1.2
        elif features.audio_source == AudioSource.MECHANICAL:
            source_factor = 0.8

        # 综合睡眠适用性得分
        overall_score = (
            stability_factor * 0.3 +
            tonal_factor * 0.3 +
            dynamic_factor * 0.25 +
            spectrum_factor * 0.15
        ) * source_factor * 100

        # 效果预测（基于科学研究）
        effectiveness_prediction = noise_effectiveness * (overall_score / 100)

        # 不同睡眠阶段的适用性
        sleep_phase_suitability = {
            'falling_asleep': overall_score * 0.9,  # 入睡阶段最重要
            'light_sleep': overall_score * 0.8,
            'deep_sleep': overall_score * 0.7,
            'rem_sleep': overall_score * 0.6
        }

        # 干扰风险评估
        disruption_risk = 0
        if features.tonal_ratio > 100:
            disruption_risk += 0.3
        if features.dynamic_range_db and features.dynamic_range_db > 20:
            disruption_risk += 0.4
        if "Sudden Events" in features.sound_tags:
            disruption_risk += 0.5
        disruption_risk = min(1.0, disruption_risk)

        # 舒适度评估
        comfort_level = overall_score / 100
        if features.audio_source == AudioSource.NATURAL:
            comfort_level *= 1.1
        comfort_level = min(1.0, comfort_level)

        # 科学证据等级
        if features.noise_type == NoiseType.PINK:
            scientific_evidence_level = "强证据支持 (82%研究有效)"
        elif features.noise_type == NoiseType.WHITE:
            scientific_evidence_level = "有限证据支持 (33%研究有效)"
        elif features.noise_type == NoiseType.GREEN:
            scientific_evidence_level = "实验性支持 (55%预估有效，需更多研究)"
        elif features.noise_type in [NoiseType.BROWN, NoiseType.DEEP_RED]:
            scientific_evidence_level = "理论支持 (低频偏好)"
        else:
            scientific_evidence_level = "证据不足"

        return SleepSuitability(
            overall_score=overall_score,
            effectiveness_prediction=effectiveness_prediction,
            sleep_phase_suitability=sleep_phase_suitability,
            disruption_risk=disruption_risk,
            comfort_level=comfort_level,
            scientific_evidence_level=scientific_evidence_level
        )

    def _generate_personalized_recommendations(
        self,
        features: AudioFeatures,
        safety: SafetyAssessment,
        suitability: SleepSuitability
    ) -> Dict[UserGroup, PersonalizedRecommendation]:
        """生成个性化推荐"""
        recommendations = {}

        for user_group in UserGroup:
            recommendations[user_group] = self._create_user_group_recommendation(
                user_group, features, safety, suitability
            )

        return recommendations

    def _create_user_group_recommendation(
        self,
        user_group: UserGroup,
        features: AudioFeatures,
        safety: SafetyAssessment,
        suitability: SleepSuitability
    ) -> PersonalizedRecommendation:
        """为特定用户群体创建推荐"""

        # 获取用户群体的安全阈值
        safety_thresholds = self.scientific_data['safety_thresholds'][user_group]

        # 基础适用性得分
        base_score = suitability.overall_score

        # 用户群体特定的调整
        if user_group == UserGroup.ADULT:
            # 成人群体：平衡效果和安全性
            suitability_score = base_score
            if features.noise_type == NoiseType.PINK:
                suitability_score *= 1.2  # 粉噪音加分
            elif features.noise_type == NoiseType.GREEN:
                suitability_score *= 1.1  # 绿噪音适度加分

            usage_recommendation = self._generate_adult_usage_recommendation(features, safety)
            benefits = ["改善入睡时间", "减少夜间觉醒", "屏蔽环境噪声"]
            if features.noise_type == NoiseType.GREEN:
                benefits.append("中频平衡，自然感强")
            risks = ["长期使用可能产生依赖"] if base_score > 60 else ["效果可能有限"]
            if features.noise_type == NoiseType.GREEN:
                risks.append("实验性功能，科学证据有限")

        elif user_group == UserGroup.INFANT:
            # 婴幼儿：安全性优先
            suitability_score = base_score * 0.7  # 更保守的评分
            if safety.volume_safety != SafetyLevel.SAFE:
                suitability_score *= 0.5

            # 绿噪音对婴儿的特殊考虑
            if features.noise_type == NoiseType.GREEN:
                suitability_score *= 0.6  # 绿噪音对婴儿更保守

            usage_recommendation = self._generate_infant_usage_recommendation(features, safety)
            benefits = ["辅助快速入睡", "创造一致的睡眠环境"]
            risks = ["需严格控制音量和距离", "避免长时间连续使用"]
            if features.noise_type == NoiseType.GREEN:
                risks.extend(["中频集中可能影响听觉发育", "建议优先选择粉噪音", "仅限短时间使用"])

        elif user_group == UserGroup.ELDERLY:
            # 老年人：重视粉噪音和低频
            suitability_score = base_score
            if features.noise_type in [NoiseType.PINK, NoiseType.BROWN]:
                suitability_score *= 1.3  # 低频噪音特别加分
            elif features.noise_type == NoiseType.GREEN:
                suitability_score *= 1.15  # 绿噪音中等加分

            usage_recommendation = self._generate_elderly_usage_recommendation(features, safety)
            benefits = ["增强深度睡眠", "改善记忆巩固", "减少环境干扰"]
            if features.noise_type == NoiseType.GREEN:
                benefits.append("中频丰富有助听觉刺激")
            risks = ["避免过度依赖", "注意听力保护"]

        elif user_group == UserGroup.INSOMNIA:
            # 失眠患者：综合考虑
            suitability_score = base_score
            if suitability.disruption_risk < 0.3:
                suitability_score *= 1.1  # 低干扰风险加分
            if features.noise_type == NoiseType.GREEN:
                suitability_score *= 1.05  # 绿噪音轻微加分

            usage_recommendation = self._generate_insomnia_usage_recommendation(features, safety)
            benefits = ["辅助放松", "屏蔽干扰", "建立睡眠仪式感"]
            if features.noise_type == NoiseType.GREEN:
                benefits.append("平衡的频谱特性有助放松")
            risks = ["仅为辅助手段", "需配合其他治疗方法"]
            if features.noise_type == NoiseType.GREEN:
                risks.append("实验性功能，效果因人而异")

        # 生成最优设置
        optimal_settings = self._generate_optimal_settings(user_group, features, safety)

        # 生成替代建议
        alternatives = self._generate_alternatives(features, suitability_score)

        # 科学依据
        scientific_rationale = self._generate_scientific_rationale(
            user_group, features, suitability
        )

        return PersonalizedRecommendation(
            user_group=user_group,
            suitability_score=min(100, max(0, suitability_score)),
            usage_recommendation=usage_recommendation,
            optimal_settings=optimal_settings,
            benefits=benefits,
            risks=risks,
            alternatives=alternatives,
            scientific_rationale=scientific_rationale
        )

    def _generate_adult_usage_recommendation(self, features: AudioFeatures, safety: SafetyAssessment) -> str:
        """生成成人使用建议"""
        if features.noise_type == NoiseType.GREEN:
            if safety.overall_safety == SafetyLevel.SAFE:
                return "可以尝试：绿噪音为实验性功能，中频平衡特性可能有助睡眠，建议音量50-60dB，注意个体差异"
            else:
                return "谨慎使用：绿噪音存在安全风险，建议选择传统的粉噪音或白噪音"
        elif safety.overall_safety == SafetyLevel.SAFE and features.noise_type == NoiseType.PINK:
            return "推荐使用：适合作为日常睡眠辅助，建议睡前30分钟开始播放"
        elif safety.overall_safety in [SafetyLevel.SAFE, SafetyLevel.CAUTION]:
            return "可以使用：注意控制音量在50-60dB，避免整夜高音量播放"
        else:
            return "不推荐使用：存在安全风险或效果有限"

    def _generate_infant_usage_recommendation(self, features: AudioFeatures, safety: SafetyAssessment) -> str:
        """生成婴幼儿使用建议"""
        if features.noise_type == NoiseType.GREEN:
            return "强烈不推荐：绿噪音中频集中可能影响婴幼儿听觉发育，建议选择粉噪音或自然水声"
        elif safety.volume_safety == SafetyLevel.SAFE and features.tonal_ratio < 10:
            return "谨慎使用：音量≤50dB，距离≥2米，仅在入睡阶段使用，入睡后关闭"
        else:
            return "不推荐使用：不符合婴幼儿安全标准"

    def _generate_elderly_usage_recommendation(self, features: AudioFeatures, safety: SafetyAssessment) -> str:
        """生成老年人使用建议"""
        if features.noise_type == NoiseType.GREEN:
            if safety.overall_safety == SafetyLevel.SAFE:
                return "可以尝试：绿噪音中频丰富可能有助听觉刺激，建议音量45-55dB，注意个体反应"
            else:
                return "谨慎使用：建议优先选择粉噪音或棕噪音"
        elif features.noise_type in [NoiseType.PINK, NoiseType.BROWN]:
            return "推荐使用：低频丰富的声音有助于深度睡眠，建议音量45-55dB"
        elif safety.overall_safety == SafetyLevel.SAFE:
            return "可以尝试：虽非最佳选择，但可作为环境噪声屏蔽"
        else:
            return "不推荐使用：建议选择更适合的粉噪音或自然声音"

    def _generate_insomnia_usage_recommendation(self, features: AudioFeatures, safety: SafetyAssessment) -> str:
        """生成失眠患者使用建议"""
        if features.noise_type == NoiseType.GREEN:
            if safety.overall_safety == SafetyLevel.SAFE:
                return "可以尝试：绿噪音平衡特性可能有助建立睡眠仪式，但为实验性功能，效果因人而异"
            else:
                return "不推荐使用：建议选择科学证据更充分的粉噪音"
        elif features.audio_source == AudioSource.NATURAL and safety.overall_safety == SafetyLevel.SAFE:
            return "推荐作为辅助：结合放松训练使用，建立固定的睡前仪式"
        elif safety.overall_safety == SafetyLevel.SAFE:
            return "可以尝试：作为环境噪声屏蔽，但需配合其他治疗方法"
        else:
            return "不推荐使用：建议咨询专业医生选择更合适的治疗方案"

    def _generate_optimal_settings(self, user_group: UserGroup, features: AudioFeatures, safety: SafetyAssessment) -> Dict:
        """生成最优设置"""
        safety_thresholds = self.scientific_data['safety_thresholds'][user_group]

        # 如果是绿噪音，使用专用安全阈值
        if features.noise_type == NoiseType.GREEN:
            green_safety = self.scientific_data['green_noise_safety'][user_group]
            settings = {
                'volume_db': min(green_safety['max_db'], safety.recommended_volume_db[1]),
                'distance_cm': max(green_safety['min_distance_cm'], safety.recommended_distance_cm),
                'duration_hours': green_safety['max_duration_hours'],
                'timing': 'bedtime_routine',
                'special_notes': green_safety['special_considerations']
            }
        else:
            settings = {
                'volume_db': min(safety_thresholds['max_db'], safety.recommended_volume_db[1]),
                'distance_cm': max(safety_thresholds['min_distance_cm'], safety.recommended_distance_cm),
                'duration_hours': safety.max_duration_hours,
                'timing': 'bedtime_routine'
            }

        # 用户群体特定调整
        if user_group == UserGroup.INFANT:
            if features.noise_type == NoiseType.GREEN:
                settings.update({
                    'volume_db': min(45, settings['volume_db']),
                    'distance_cm': max(200, settings['distance_cm']),
                    'duration_hours': 1.0,  # 绿噪音对婴儿更严格
                    'timing': 'sleep_onset_only',
                    'warning': '绿噪音不推荐用于婴幼儿'
                })
            else:
                settings.update({
                    'volume_db': min(50, settings['volume_db']),
                    'distance_cm': max(200, settings['distance_cm']),
                    'duration_hours': 1.0,  # 仅入睡阶段
                    'timing': 'sleep_onset_only'
                })
        elif user_group == UserGroup.ELDERLY:
            settings.update({
                'volume_db': min(55, settings['volume_db']),
                'timing': 'full_night_if_needed'
            })

        return settings

    def _generate_alternatives(self, features: AudioFeatures, suitability_score: float) -> List[str]:
        """生成替代建议"""
        alternatives = []

        if suitability_score < 40:
            alternatives.append("寻找粉噪音或自然水声类音频")
            alternatives.append("考虑使用专业的白噪音设备")

        if features.noise_type != NoiseType.PINK:
            alternatives.append("尝试粉噪音（科学证据最强）")

        if features.audio_source != AudioSource.NATURAL:
            alternatives.append("选择自然声音（如雨声、海浪声）")

        if features.dynamic_range_db and features.dynamic_range_db > 15:
            alternatives.append("寻找动态范围更小的音频")

        if not alternatives:
            alternatives.append("当前音频已较为适合")

        return alternatives

    def _generate_scientific_rationale(self, user_group: UserGroup, features: AudioFeatures, suitability: SleepSuitability) -> str:
        """生成科学依据说明"""
        rationale_parts = []

        # 噪音类型的科学依据
        if features.noise_type == NoiseType.PINK:
            rationale_parts.append("粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%")
        elif features.noise_type == NoiseType.WHITE:
            rationale_parts.append("白噪音仅在33%的研究中显示有效，效果有限")
        elif features.noise_type == NoiseType.GREEN:
            rationale_parts.append("绿噪音为新兴概念，中频集中特性预估55%有效率，但缺乏专门临床研究")

        # 用户群体特定的科学依据
        if user_group == UserGroup.INFANT:
            rationale_parts.append("研究显示80%的新生儿在5分钟内被白噪音哄睡，但需严格控制安全参数")
        elif user_group == UserGroup.ELDERLY:
            rationale_parts.append("粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现")
        elif user_group == UserGroup.INSOMNIA:
            rationale_parts.append("白噪音可使失眠患者入睡时间缩短38%，但仅作为辅助治疗")

        # 自然声音的优势
        if features.audio_source == AudioSource.NATURAL:
            rationale_parts.append("自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好")

        return "；".join(rationale_parts) if rationale_parts else "缺乏充分的科学证据支持"

    def _generate_overall_recommendation(self, suitability: SleepSuitability, safety: SafetyAssessment) -> str:
        """生成总体推荐"""
        if safety.overall_safety == SafetyLevel.DANGEROUS:
            return "❌ 不推荐使用：存在安全风险"
        elif suitability.overall_score >= 70 and safety.overall_safety == SafetyLevel.SAFE:
            return "✅ 强烈推荐：科学证据支持，安全性良好"
        elif suitability.overall_score >= 50 and safety.overall_safety in [SafetyLevel.SAFE, SafetyLevel.CAUTION]:
            return "⚠️ 可以使用：有一定效果，注意使用方法"
        elif suitability.overall_score >= 30:
            return "🤔 谨慎使用：效果有限，建议寻找更好的替代方案"
        else:
            return "❌ 不推荐使用：效果差或存在风险"

    def _get_scientific_references(self) -> List[str]:
        """获取科学参考文献"""
        return [
            "粉噪音82%有效率 vs 白噪音33%有效率 - 多项睡眠研究荟萃分析",
            "婴幼儿白噪音安全标准：音量≤50dB，距离≥2米 - 儿科睡眠医学研究",
            "老年人粉噪音深度睡眠改善 - 神经科学睡眠研究",
            "失眠患者入睡时间缩短38% - 临床睡眠医学试验",
            "绿噪音中频集中特性与自然环境音模拟 - 心理声学理论分析",
            "自然声音偏好与低频丰富特性 - 心理声学研究"
        ]

    def generate_detailed_report(self, report: ComprehensiveReport) -> str:
        """生成详细的文本报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("🧠 智能睡眠音频评估与推荐报告")
        lines.append("=" * 80)
        lines.append(f"📁 音频文件: {report.audio_file}")
        lines.append(f"⏰ 分析时间: {report.analysis_timestamp}")
        lines.append(f"🎯 总体推荐: {report.overall_recommendation}")
        lines.append("")

        # 音频特征分析
        lines.append("📊 音频特征分析")
        lines.append("-" * 40)
        features = report.audio_features
        lines.append(f"🎵 噪音类型: {features.noise_type.value}")
        lines.append(f"🔊 音频来源: {features.audio_source.value}")
        lines.append(f"🏷️  声音标签: {', '.join(features.sound_tags)}")
        lines.append(f"📈 频谱斜率: {features.spectral_slope:.3f}")
        lines.append(f"📊 响度稳定性: {features.loudness_stability:.3f}")
        lines.append(f"🎼 音调峰值比: {features.tonal_ratio:.2f}")
        lines.append(f"📏 动态范围: {features.dynamic_range_db:.1f} dB" if features.dynamic_range_db else "📏 动态范围: 未检测")
        lines.append(f"⏱️  音频时长: {features.duration_seconds:.1f} 秒")
        lines.append("")

        # 安全评估
        lines.append("🛡️ 安全评估")
        lines.append("-" * 40)
        safety = report.safety_assessment
        lines.append(f"🔒 总体安全: {safety.overall_safety.value}")
        lines.append(f"🔊 音量安全: {safety.volume_safety.value}")
        lines.append(f"⏰ 时长安全: {safety.duration_safety.value}")
        lines.append(f"📋 内容安全: {safety.content_safety.value}")
        lines.append(f"📊 安全得分: {safety.safety_score:.1f}/100")
        lines.append(f"🎚️  推荐音量: {safety.recommended_volume_db[0]}-{safety.recommended_volume_db[1]} dB")
        lines.append(f"📏 推荐距离: ≥{safety.recommended_distance_cm} cm")
        if safety.warnings:
            lines.append("⚠️  安全警告:")
            for warning in safety.warnings:
                lines.append(f"   • {warning}")
        lines.append("")

        # 睡眠适用性
        lines.append("😴 睡眠适用性评估")
        lines.append("-" * 40)
        suitability = report.sleep_suitability
        lines.append(f"📊 总体得分: {suitability.overall_score:.1f}/100")
        lines.append(f"🎯 效果预测: {suitability.effectiveness_prediction:.1%}")
        lines.append(f"⚠️  干扰风险: {suitability.disruption_risk:.1%}")
        lines.append(f"😌 舒适度: {suitability.comfort_level:.1%}")
        lines.append(f"🔬 科学证据: {suitability.scientific_evidence_level}")
        lines.append("")

        # 个性化推荐
        lines.append("👥 个性化推荐")
        lines.append("-" * 40)
        for user_group, recommendation in report.personalized_recommendations.items():
            lines.append(f"👤 {user_group.value}")
            lines.append(f"   📊 适用性得分: {recommendation.suitability_score:.1f}/100")
            lines.append(f"   💡 使用建议: {recommendation.usage_recommendation}")
            lines.append(f"   ⚙️  最优设置:")
            for key, value in recommendation.optimal_settings.items():
                lines.append(f"      • {key}: {value}")
            lines.append(f"   ✅ 潜在益处: {', '.join(recommendation.benefits)}")
            lines.append(f"   ⚠️  注意风险: {', '.join(recommendation.risks)}")
            lines.append(f"   🔄 替代建议: {', '.join(recommendation.alternatives)}")
            lines.append(f"   🔬 科学依据: {recommendation.scientific_rationale}")
            lines.append("")

        # 科学参考
        lines.append("📚 科学参考文献")
        lines.append("-" * 40)
        for i, ref in enumerate(report.scientific_references, 1):
            lines.append(f"{i}. {ref}")
        lines.append("")
        lines.append("=" * 80)

        return "\n".join(lines)
