#!/usr/bin/env python3
"""
P1任务测试套件：功能集成统一验证
测试新增的4个核心功能和向后兼容性
"""

import subprocess
import json
import time
import sys
from pathlib import Path

def run_command(cmd, timeout=60):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd="/Users/<USER>/Documents/Noise"
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "命令超时"

def test_new_parameters():
    """测试新增参数的帮助信息"""
    print("🧪 测试新增参数...")
    
    returncode, stdout, stderr = run_command("python3 run_sleep_audio_analysis.py --help")
    
    if returncode != 0:
        print(f"❌ 帮助命令失败: {stderr}")
        return False
    
    # 检查新参数是否存在
    required_params = [
        "--format {text,json,markdown}",
        "--auto-name",
        "--comparison", 
        "--template {standard,research,clinical,consumer}"
    ]
    
    missing_params = []
    for param in required_params:
        if param not in stdout:
            missing_params.append(param)
    
    if missing_params:
        print(f"❌ 缺少参数: {missing_params}")
        return False
    
    print("✅ 所有新参数都已正确添加")
    return True

def test_markdown_output():
    """测试Markdown输出功能"""
    print("\n🧪 测试Markdown输出功能...")
    
    # 测试单文件Markdown输出
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format markdown --auto-name"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode != 0:
        print(f"❌ Markdown输出失败: {stderr}")
        return False
    
    # 检查是否生成了文件
    if "报告已保存到:" not in stdout:
        print("❌ 没有生成Markdown文件")
        return False
    
    # 提取文件名
    lines = stdout.split('\n')
    report_line = [line for line in lines if "报告已保存到:" in line]
    if not report_line:
        print("❌ 无法找到报告文件路径")
        return False
    
    filename = report_line[0].split(": ")[1]
    filepath = Path(filename)
    
    if not filepath.exists():
        print(f"❌ Markdown文件不存在: {filename}")
        return False
    
    # 检查文件内容
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_sections = [
        "# 🧠 智能睡眠音频评估系统",
        "## 📋 报告信息",
        "## 🏆 分析结果汇总",
        "## 🔬 科学依据与使用建议"
    ]
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    if missing_sections:
        print(f"❌ Markdown文件缺少章节: {missing_sections}")
        return False
    
    print(f"✅ Markdown输出功能正常，文件: {filename}")
    return True

def test_auto_naming():
    """测试自动命名功能"""
    print("\n🧪 测试自动命名功能...")
    
    # 测试不同格式的自动命名
    formats = ['text', 'json', 'markdown']
    
    for fmt in formats:
        cmd = f"python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format {fmt} --auto-name"
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode != 0:
            print(f"❌ {fmt}格式自动命名失败: {stderr}")
            return False
        
        # 检查文件名格式
        if "报告已保存到:" not in stdout:
            print(f"❌ {fmt}格式没有生成文件")
            return False
        
        lines = stdout.split('\n')
        report_line = [line for line in lines if "报告已保存到:" in line]
        filename = report_line[0].split(": ")[1]
        
        # 检查文件名格式
        expected_pattern = f"analysis_report_"
        if not filename.startswith(expected_pattern):
            print(f"❌ {fmt}格式文件名格式错误: {filename}")
            return False
        
        # 检查扩展名
        expected_ext = {'text': '.txt', 'json': '.json', 'markdown': '.md'}[fmt]
        if not filename.endswith(expected_ext):
            print(f"❌ {fmt}格式扩展名错误: {filename}")
            return False
        
        print(f"✅ {fmt}格式自动命名正常: {filename}")
    
    return True

def test_comparison_analysis():
    """测试对比分析功能"""
    print("\n🧪 测试对比分析功能...")
    
    # 测试批量分析的对比功能
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise --all --format markdown --comparison --auto-name"
    returncode, stdout, stderr = run_command(cmd, timeout=120)
    
    if returncode != 0:
        print(f"❌ 对比分析失败: {stderr}")
        return False
    
    # 检查是否生成了文件
    if "报告已保存到:" not in stdout:
        print("❌ 对比分析没有生成文件")
        return False
    
    # 提取文件名并检查内容
    lines = stdout.split('\n')
    report_line = [line for line in lines if "报告已保存到:" in line]
    filename = report_line[0].split(": ")[1]
    filepath = Path(filename)
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查对比分析相关内容
    comparison_sections = [
        "## 📊 技术参数对比分析",
        "### ⚙️ 详细技术参数",
        "### 🔬 科学有效性对比"
    ]
    
    missing_sections = []
    for section in comparison_sections:
        if section not in content:
            missing_sections.append(section)
    
    if missing_sections:
        print(f"❌ 对比分析缺少章节: {missing_sections}")
        return False
    
    print(f"✅ 对比分析功能正常，文件: {filename}")
    return True

def test_template_system():
    """测试模板系统"""
    print("\n🧪 测试模板系统...")
    
    templates = ['standard', 'research', 'clinical', 'consumer']
    
    for template in templates:
        cmd = f"python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format markdown --template {template} --auto-name"
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode != 0:
            print(f"❌ {template}模板失败: {stderr}")
            return False
        
        # 检查是否生成了文件
        if "报告已保存到:" not in stdout:
            print(f"❌ {template}模板没有生成文件")
            return False
        
        # 检查文件内容
        lines = stdout.split('\n')
        report_line = [line for line in lines if "报告已保存到:" in line]
        filename = report_line[0].split(": ")[1]
        filepath = Path(filename)
        
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查模板特定的标题
        template_titles = {
            'standard': "# 🧠 智能睡眠音频评估系统 - 分析报告",
            'research': "# 🔬 智能睡眠音频评估系统 - 科研分析报告",
            'clinical': "# 🏥 智能睡眠音频评估系统 - 临床评估报告",
            'consumer': "# 🏠 智能睡眠音频评估系统 - 消费者指南"
        }
        
        expected_title = template_titles[template]
        if expected_title not in content:
            print(f"❌ {template}模板标题错误")
            return False
        
        print(f"✅ {template}模板正常工作")
    
    return True

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性...")
    
    # 测试所有原有命令是否仍然工作
    old_commands = [
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav",
        "python3 run_sleep_audio_analysis.py Sounds/Noise --all",
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format json",
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --detailed",
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --user-group adult"
    ]
    
    for cmd in old_commands:
        returncode, stdout, stderr = run_command(cmd, timeout=90)
        
        if returncode != 0:
            print(f"❌ 向后兼容性失败: {cmd}")
            print(f"   错误: {stderr}")
            return False
        
        # 检查基本输出
        if "智能睡眠音频评估系统" not in stdout:
            print(f"❌ 输出格式异常: {cmd}")
            return False
        
        print(f"✅ 兼容性测试通过: {cmd.split()[-1]}")
    
    return True

def test_performance():
    """测试性能"""
    print("\n🧪 测试性能...")
    
    # 测试单文件分析性能
    start_time = time.time()
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format markdown"
    returncode, stdout, stderr = run_command(cmd)
    end_time = time.time()
    
    if returncode != 0:
        print(f"❌ 性能测试失败: {stderr}")
        return False
    
    single_file_time = end_time - start_time
    
    # 测试批量分析性能
    start_time = time.time()
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise --all --format markdown --comparison"
    returncode, stdout, stderr = run_command(cmd, timeout=120)
    end_time = time.time()
    
    if returncode != 0:
        print(f"❌ 批量性能测试失败: {stderr}")
        return False
    
    batch_time = end_time - start_time
    
    print(f"📊 性能测试结果:")
    print(f"   单文件分析: {single_file_time:.2f}秒")
    print(f"   批量分析(3文件): {batch_time:.2f}秒")
    print(f"   平均每文件: {batch_time/3:.2f}秒")
    
    # 性能要求：单文件<30秒，批量平均<20秒/文件
    if single_file_time > 30:
        print(f"❌ 单文件性能不达标: {single_file_time:.2f}秒 > 30秒")
        return False
    
    if batch_time/3 > 20:
        print(f"❌ 批量性能不达标: {batch_time/3:.2f}秒/文件 > 20秒/文件")
        return False
    
    print("✅ 性能测试通过")
    return True

def main():
    """主测试函数"""
    print("🧠 P1任务测试套件：功能集成统一验证")
    print("=" * 60)
    
    tests = [
        ("新增参数验证", test_new_parameters),
        ("Markdown输出功能", test_markdown_output),
        ("自动命名功能", test_auto_naming),
        ("对比分析功能", test_comparison_analysis),
        ("模板系统", test_template_system),
        ("向后兼容性", test_backward_compatibility),
        ("性能测试", test_performance),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔬 运行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                failed += 1
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - 异常: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！P1任务功能集成统一 - 完成")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
