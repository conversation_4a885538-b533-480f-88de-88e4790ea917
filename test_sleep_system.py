#!/usr/bin/env python3
"""
测试智能睡眠音频评估系统
"""

import os
import sys
from pathlib import Path

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")
    
    try:
        import numpy as np
        print("✅ numpy 导入成功")
    except Exception as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        import librosa
        print("✅ librosa 导入成功")
    except Exception as e:
        print(f"❌ librosa 导入失败: {e}")
        return False
    
    try:
        from scipy import signal
        from scipy.stats import linregress
        print("✅ scipy 导入成功")
    except Exception as e:
        print(f"❌ scipy 导入失败: {e}")
        return False
    
    try:
        from noise_analyzer import (
            apply_a_weighting, calculate_bark_spectrum, calculate_bark_spectral_balance,
            calculate_snr, calculate_dynamic_range, detect_clipping, 
            detect_spectral_distortion, assess_background_noise
        )
        print("✅ noise_analyzer 导入成功")
    except Exception as e:
        print(f"❌ noise_analyzer 导入失败: {e}")
        return False
    
    try:
        from smart_sleep_audio_system import SmartSleepAudioSystem
        print("✅ smart_sleep_audio_system 导入成功")
    except Exception as e:
        print(f"❌ smart_sleep_audio_system 导入失败: {e}")
        return False
    
    return True

def test_single_file():
    """测试单个文件分析"""
    print("\n🔍 测试单个文件分析...")
    
    try:
        from smart_sleep_audio_system import SmartSleepAudioSystem
        
        # 初始化系统
        system = SmartSleepAudioSystem()
        print("✅ 系统初始化成功")
        
        # 查找测试文件
        test_files = list(Path("noisekun").glob("*.ogm"))
        if not test_files:
            print("❌ 未找到测试文件")
            return False
        
        test_file = test_files[0]
        print(f"🎵 测试文件: {test_file}")
        
        # 分析文件
        report = system.analyze_audio_file(str(test_file))
        print("✅ 文件分析成功")
        
        # 显示基本结果
        print(f"📊 睡眠适用性得分: {report.sleep_suitability.overall_score:.1f}/100")
        print(f"🛡️ 安全等级: {report.safety_assessment.overall_safety.value}")
        print(f"🎵 噪音类型: {report.audio_features.noise_type.value}")
        print(f"🔊 音频来源: {report.audio_features.audio_source.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单文件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_analysis():
    """测试批量分析"""
    print("\n🔍 测试批量分析...")
    
    try:
        from smart_sleep_audio_system import SmartSleepAudioSystem
        
        system = SmartSleepAudioSystem()
        
        # 获取所有测试文件
        test_files = list(Path("noisekun").glob("*.ogm"))
        print(f"📁 找到 {len(test_files)} 个测试文件")
        
        results = []
        for i, test_file in enumerate(test_files[:3], 1):  # 只测试前3个文件
            print(f"🔍 分析文件 {i}/3: {test_file.name}")
            
            try:
                report = system.analyze_audio_file(str(test_file))
                results.append(report)
                
                score = report.sleep_suitability.overall_score
                safety = report.safety_assessment.overall_safety.value
                print(f"   📊 得分: {score:.1f}/100, 🛡️ 安全: {safety}")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
                continue
        
        print(f"✅ 批量分析完成，成功分析 {len(results)} 个文件")
        return True
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_report_generation():
    """测试报告生成"""
    print("\n🔍 测试报告生成...")
    
    try:
        from smart_sleep_audio_system import SmartSleepAudioSystem
        
        system = SmartSleepAudioSystem()
        
        # 分析一个文件
        test_files = list(Path("noisekun").glob("*.ogm"))
        if not test_files:
            print("❌ 未找到测试文件")
            return False
        
        report = system.analyze_audio_file(str(test_files[0]))
        
        # 生成详细报告
        detailed_report = system.generate_detailed_report(report)
        
        # 保存报告
        with open("test_sleep_report.txt", "w", encoding="utf-8") as f:
            f.write(detailed_report)
        
        print("✅ 详细报告生成成功")
        print(f"📄 报告已保存到: test_sleep_report.txt")
        print(f"📏 报告长度: {len(detailed_report)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧠 智能睡眠音频评估系统 - 功能测试")
    print("=" * 60)
    
    # 检查工作目录
    if not Path("noisekun").exists():
        print("❌ 未找到 noisekun 目录")
        sys.exit(1)
    
    # 运行测试
    tests = [
        ("导入测试", test_imports),
        ("单文件分析测试", test_single_file),
        ("批量分析测试", test_batch_analysis),
        ("报告生成测试", test_report_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*60}")
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
