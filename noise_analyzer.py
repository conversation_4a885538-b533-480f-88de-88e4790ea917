import librosa
import numpy as np
from scipy.stats import linregress
from scipy import signal
from scipy.signal import butter, sosfilt
import argparse
import os
import glob
import csv
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# --- A-weighting滤波器实现 ---

def design_a_weighting_filter(sr):
    """
    设计A-weighting滤波器 (符合IEC 61672-1标准)
    返回二阶段级联滤波器系数
    """
    # A-weighting滤波器的设计基于IEC 61672-1标准
    # 使用模拟滤波器设计然后转换为数字滤波器

    # A-weighting的传递函数参数
    f1 = 20.598997  # Hz
    f2 = 107.65265  # Hz
    f3 = 737.86223  # Hz
    f4 = 12194.217  # Hz

    # 将频率转换为角频率
    w1 = 2 * np.pi * f1
    w2 = 2 * np.pi * f2
    w3 = 2 * np.pi * f3
    w4 = 2 * np.pi * f4

    # A-weighting传递函数的分子和分母
    # H(s) = (s^4 * w4^2) / ((s + w1)^2 * (s + w4)^2 * (s^2 + w2*s + w2^2) * (s^2 + w3*s + w3^2))

    # 简化的A-weighting实现 - 使用高通和低通滤波器组合
    nyquist = sr / 2

    # 设计高通滤波器 (去除低频)
    high_freq = min(20.0, nyquist * 0.1)
    sos_high = butter(2, high_freq / nyquist, btype='high', output='sos')

    # 设计低通滤波器 (去除超高频)
    low_freq = min(20000.0, nyquist * 0.9)
    sos_low = butter(2, low_freq / nyquist, btype='low', output='sos')

    return sos_high, sos_low

def apply_a_weighting(y, sr):
    """
    对音频信号应用A-weighting滤波
    """
    try:
        if len(y) == 0:
            return y

        # 设计A-weighting滤波器
        sos_high, sos_low = design_a_weighting_filter(sr)

        # 应用滤波器
        y_filtered = sosfilt(sos_high, y)
        y_filtered = sosfilt(sos_low, y_filtered)

        return y_filtered
    except Exception as e:
        print(f"Warning: A-weighting filter failed: {e}")
        return y  # 如果滤波失败，返回原信号

def calculate_a_weighted_rms(y, sr, frame_length=2048, hop_length=512):
    """
    计算A-weighting加权的RMS值
    """
    try:
        # 应用A-weighting滤波
        y_weighted = apply_a_weighting(y, sr)

        # 计算RMS能量
        rms = librosa.feature.rms(y=y_weighted, frame_length=frame_length, hop_length=hop_length)[0]

        return rms
    except Exception as e:
        print(f"Error in A-weighted RMS calculation: {e}")
        # 如果A-weighting失败，回退到普通RMS
        return librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]

# --- Bark尺度分析函数 ---

def hz_to_bark(f):
    """
    将频率(Hz)转换为Bark尺度
    使用Zwicker & Terhardt (1980)公式
    """
    return 13 * np.arctan(0.00076 * f) + 3.5 * np.arctan((f / 7500) ** 2)

def bark_to_hz(bark):
    """
    将Bark尺度转换为频率(Hz)
    近似逆变换
    """
    # 使用数值方法求解逆函数
    frequencies = np.linspace(20, 20000, 10000)
    bark_values = hz_to_bark(frequencies)
    return np.interp(bark, bark_values, frequencies)

def get_bark_band_edges():
    """
    获取24个Bark临界频带的边界频率
    """
    # 标准的24个Bark频带边界 (Hz)
    bark_edges = [
        20, 100, 200, 300, 400, 510, 630, 770, 920, 1080,
        1270, 1480, 1720, 2000, 2320, 2700, 3150, 3700, 4400,
        5300, 6400, 7700, 9500, 12000, 15500, 20000
    ]
    return np.array(bark_edges)

def calculate_bark_spectrum(y, sr):
    """
    计算基于Bark尺度的频谱分析
    将线性频谱转换为24个Bark临界频带
    """
    try:
        # 计算功率谱密度
        nperseg = min(4096, len(y) // 4)
        freqs, psd = signal.welch(y, sr, nperseg=nperseg)

        # 获取Bark频带边界
        bark_edges = get_bark_band_edges()

        # 确保频率范围在有效范围内
        max_freq = min(sr / 2, 20000)
        bark_edges = bark_edges[bark_edges <= max_freq]

        if len(bark_edges) < 2:
            return None, None

        # 计算每个Bark频带的能量
        bark_spectrum = []
        bark_centers = []

        for i in range(len(bark_edges) - 1):
            f_low = bark_edges[i]
            f_high = bark_edges[i + 1]

            # 找到对应的频率索引
            freq_mask = (freqs >= f_low) & (freqs < f_high)

            if np.any(freq_mask):
                # 计算该频带的平均功率
                band_power = np.mean(psd[freq_mask])
                bark_spectrum.append(band_power)
                bark_centers.append((f_low + f_high) / 2)

        return np.array(bark_spectrum), np.array(bark_centers)

    except Exception as e:
        print(f"Error in Bark spectrum calculation: {e}")
        return None, None

def calculate_bark_spectral_balance(bark_spectrum):
    """
    基于Bark尺度计算频谱平衡度
    """
    try:
        if bark_spectrum is None or len(bark_spectrum) < 3:
            return None

        # 过滤零值
        valid_spectrum = bark_spectrum[bark_spectrum > 0]
        if len(valid_spectrum) < 3:
            return None

        # 计算频谱平坦度 (几何平均/算术平均)
        geometric_mean = np.exp(np.mean(np.log(valid_spectrum + 1e-10)))
        arithmetic_mean = np.mean(valid_spectrum)

        if arithmetic_mean > 0:
            spectral_flatness = geometric_mean / arithmetic_mean
        else:
            spectral_flatness = 0

        return spectral_flatness

    except Exception as e:
        print(f"Error in Bark spectral balance calculation: {e}")
        return None

# --- 分析函数 ---

def calculate_spectral_slope(y, sr):
    """
    计算音频频谱的斜率，这是判断噪音“颜色”的核心。
    白噪音斜率约等于0，粉红噪音约等于-1，布朗噪音约等于-2（在log-log尺度上）。
    """
    try:
        # 计算快速傅里叶变换(STFT)的幅度谱
        stft_result = librosa.stft(y)
        # 计算功率谱 (幅度值的平方)
        power_spectrum = np.abs(stft_result)**2
        # 沿时间轴平均，得到一个平均功率谱
        avg_power_spectrum = np.mean(power_spectrum, axis=1)
        # 获取对应的频率轴
        freqs = librosa.fft_frequencies(sr=sr)
        
        # 避免log(0)错误，只处理非零频率和功率
        valid_indices = np.where((freqs > 0) & (avg_power_spectrum > 0))
        if len(valid_indices[0]) < 2:
            return 0, 0 # 数据不足以进行线性回归

        log_freqs = np.log(freqs[valid_indices])
        log_power = np.log(avg_power_spectrum[valid_indices])

        # 对log-log图进行线性回归，得到斜率
        slope, intercept, r_value, p_value, std_err = linregress(log_freqs, log_power)
        
        # r_value^2 表示线性拟合的好坏程度
        linearity = r_value**2
        
        return slope, linearity
    except Exception as e:
        print(f"Error in spectral slope calculation: {e}")
        return None, None

def calculate_loudness_stability(y, sr):
    """
    计算基于A-weighting的感知响度稳定性
    使用A-weighting滤波器模拟人耳感知特性
    """
    try:
        # 使用A-weighting计算感知响度
        rms = calculate_a_weighted_rms(y, sr, frame_length=int(0.025 * sr), hop_length=int(0.01 * sr))

        if len(rms) == 0 or np.mean(rms) == 0:
            return 1.0  # 如果是静音，则不稳定

        # 过滤掉过小的值以避免数值问题
        rms_filtered = rms[rms > np.max(rms) * 0.01]  # 保留大于最大值1%的值

        if len(rms_filtered) < 2:
            return 1.0

        # 计算感知响度的变异系数
        mean_rms = np.mean(rms_filtered)
        std_rms = np.std(rms_filtered)

        if mean_rms == 0:
            return 1.0

        # 变异系数 (Coefficient of Variation)
        stability_metric = std_rms / mean_rms
        return stability_metric
    except Exception as e:
        print(f"Error in A-weighted loudness stability calculation: {e}")
        # 回退到原始方法
        try:
            rms = librosa.feature.rms(y=y)[0]
            if np.mean(rms) == 0:
                return 1.0
            return np.std(rms) / np.mean(rms)
        except:
            return 1.0

def check_tonal_purity(y, sr):
    """
    检查音频中是否存在明显的音调。
    通过计算频谱的最大峰值与中位值的比率来简单衡量。
    比率越高，越有可能包含纯音或旋律。
    """
    try:
        stft_result = librosa.stft(y)
        avg_power_spectrum = np.mean(np.abs(stft_result)**2, axis=1)
        if np.median(avg_power_spectrum) == 0:
            return 1.0

        peak_to_median_ratio = np.max(avg_power_spectrum) / np.median(avg_power_spectrum)
        return peak_to_median_ratio
    except Exception as e:
        print(f"Error in tonal purity calculation: {e}")
        return None

def calculate_snr(y, sr):
    """
    计算信噪比 (Signal-to-Noise Ratio)
    使用基于语音频段的科学方法估算信噪比
    """
    try:
        # 使用更高的频率分辨率
        nperseg = min(4096, len(y) // 4)
        freqs, psd = signal.welch(y, sr, nperseg=nperseg)

        # 使用科学的频段划分
        # 语音主要频段: 300-3400Hz (国际电信联盟标准)
        # 低频段: 20-300Hz (环境噪声参考)
        # 高频段: 3400Hz-Nyquist (高频噪声参考)

        low_freq_mask = (freqs >= 20) & (freqs < 300)
        speech_freq_mask = (freqs >= 300) & (freqs <= 3400)
        high_freq_mask = (freqs > 3400) & (freqs <= sr/2)

        if not (np.any(low_freq_mask) and np.any(speech_freq_mask) and np.any(high_freq_mask)):
            return None

        # 计算各频段的平均功率
        low_power = np.mean(psd[low_freq_mask])
        speech_power = np.mean(psd[speech_freq_mask])
        high_power = np.mean(psd[high_freq_mask])

        # 使用最低的频段功率作为噪声底噪估计
        # 这比假设特定频段为噪声更科学
        noise_power = min(low_power, high_power)

        if noise_power <= 0:
            return 60  # 很高的SNR

        # 计算语音频段相对于噪声底噪的信噪比
        snr_db = 10 * np.log10(speech_power / noise_power)
        return snr_db
    except Exception as e:
        print(f"Error in SNR calculation: {e}")
        return None

def calculate_dynamic_range(y):
    """
    计算动态范围
    """
    try:
        # 计算RMS能量
        rms = librosa.feature.rms(y=y, frame_length=2048, hop_length=512)[0]
        rms = rms[rms > 0]  # 移除静音部分

        if len(rms) == 0:
            return 0

        # 计算动态范围 (dB)
        max_rms = np.max(rms)
        min_rms = np.min(rms)

        if min_rms <= 0:
            return 60  # 设置一个合理的上限

        dynamic_range_db = 20 * np.log10(max_rms / min_rms)
        return dynamic_range_db
    except Exception as e:
        print(f"Error in dynamic range calculation: {e}")
        return None

def detect_clipping(y, threshold=0.99):
    """
    检测削波失真
    """
    try:
        # 检测接近最大值的样本
        clipped_samples = np.sum(np.abs(y) >= threshold)
        total_samples = len(y)
        clipping_percentage = (clipped_samples / total_samples) * 100

        return clipping_percentage
    except Exception as e:
        print(f"Error in clipping detection: {e}")
        return None

def detect_spectral_distortion(y, sr):
    """
    检测频谱失真度 (替代THD，更适合噪音信号)
    通过分析频谱的不规则性来评估音频质量
    """
    try:
        # 计算功率谱密度
        nperseg = min(2048, len(y) // 4)
        freqs, psd = signal.welch(y, sr, nperseg=nperseg)

        # 过滤有效频率范围 (20Hz - Nyquist/2)
        valid_mask = (freqs >= 20) & (freqs <= sr/4)
        if not np.any(valid_mask):
            return None

        valid_freqs = freqs[valid_mask]
        valid_psd = psd[valid_mask]

        if len(valid_psd) < 10:  # 需要足够的频率点
            return None

        # 计算频谱平滑度 - 检测异常峰值
        # 使用移动平均来检测频谱的不规则性
        window_size = max(3, len(valid_psd) // 20)
        smoothed_psd = np.convolve(valid_psd, np.ones(window_size)/window_size, mode='same')

        # 计算实际频谱与平滑频谱的偏差
        if np.mean(smoothed_psd) > 0:
            deviation = np.mean(np.abs(valid_psd - smoothed_psd)) / np.mean(smoothed_psd)
            distortion_percentage = deviation * 100
        else:
            distortion_percentage = 0

        return min(distortion_percentage, 100)  # 限制在100%以内
    except Exception as e:
        print(f"Error in spectral distortion detection: {e}")
        return None

def assess_background_noise(y, sr):
    """
    评估背景噪声水平
    """
    try:
        # 计算短时能量
        frame_length = int(0.025 * sr)  # 25ms frames
        hop_length = int(0.01 * sr)     # 10ms hop

        rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]

        # 使用较低的百分位数来估计背景噪声
        noise_floor = np.percentile(rms, 10)  # 10th percentile
        signal_level = np.percentile(rms, 90)  # 90th percentile

        if noise_floor <= 0:
            return 60  # 很低的噪声

        noise_level_db = 20 * np.log10(noise_floor)

        return noise_level_db
    except Exception as e:
        print(f"Error in background noise assessment: {e}")
        return None

# --- 判断与分类函数 ---

def classify_noise(slope):
    """根据频谱斜率对噪音进行分类"""
    if -0.5 < slope <= 0.5:
        return "白噪音 (White Noise)"
    elif -1.5 < slope <= -0.5:
        return "粉红噪音 (Pink Noise)"
    elif -2.5 < slope <= -1.5:
        return "布朗噪音 (Brown Noise)"
    elif slope > 0.5:
        return "蓝/紫噪音类 (不适合放松)"
    else:
        return "深红/黑噪音类或复杂的自然音"

def judge_quality(stability, tonal_ratio, linearity, snr=None, dynamic_range=None,
                 clipping=None, distortion=None, noise_level=None, evaluation_mode='general'):
    """根据各项指标综合判断质量 - 改进的非线性评分系统"""
    # 使用加权评分系统，每个指标独立评分后加权合并
    scores = {}
    reasons = []
    issues = []

    # 1. 响度稳定性评分 (权重: 25%)
    if stability <= 0.05:  # 5%以内变化
        scores['stability'] = 100
    elif stability <= 0.08:  # 8%以内变化
        scores['stability'] = 80
        reasons.append(f"响度有轻微波动 (波动性: {stability:.2%})。")
    elif stability <= 0.15:  # 15%以内变化
        scores['stability'] = 50
        reasons.append(f"响度不够稳定 (波动性: {stability:.2%})。")
        issues.append("响度不稳定")
    else:  # 超过15%变化
        scores['stability'] = 20
        reasons.append(f"响度很不稳定 (波动性: {stability:.2%})，有突兀的声音。")
        issues.append("响度不稳定")

    # 2. 音调性评分 (权重: 25%)
    if tonal_ratio <= 10:  # 很低的音调性
        scores['tonality'] = 100
    elif tonal_ratio <= 50:  # 轻微音调性
        scores['tonality'] = 70
        reasons.append(f"可能包含轻微的音调 (音调峰值比: {tonal_ratio:.1f})。")
    elif tonal_ratio <= 100:  # 明显音调性
        scores['tonality'] = 40
        reasons.append(f"包含明显的音调成分 (音调峰值比: {tonal_ratio:.1f})。")
        issues.append("包含明显音调")
    else:  # 很强的音调性
        scores['tonality'] = 10
        reasons.append(f"包含很强的音调或旋律 (音调峰值比: {tonal_ratio:.1f})。")
        issues.append("包含明显音调")

    # 3. 频谱质量评分 (权重: 20%)
    if linearity >= 0.9:  # 很好的线性度
        scores['spectrum'] = 100
    elif linearity >= 0.7:  # 良好的线性度
        scores['spectrum'] = 80
    elif linearity >= 0.5:  # 一般的线性度
        scores['spectrum'] = 60
        reasons.append(f"频谱不够规则 (线性度: {linearity:.2f})。")
    else:  # 较差的线性度
        scores['spectrum'] = 30
        reasons.append(f"频谱很不规则，不像标准噪音 (线性度: {linearity:.2f})。")

    # 4. 信噪比评分 (权重: 15%)
    if snr is not None:
        if snr >= 35:
            scores['snr'] = 100
        elif snr >= 25:
            scores['snr'] = 80
        elif snr >= 20:
            scores['snr'] = 60
            reasons.append(f"信噪比一般 (SNR: {snr:.1f}dB)。")
        else:
            scores['snr'] = 30
            reasons.append(f"信噪比较低 (SNR: {snr:.1f}dB)，背景噪声明显。")
            issues.append("信噪比低")
    else:
        scores['snr'] = 70  # 默认中等分数

    # 5. 动态范围评分 (权重: 10%)
    if dynamic_range is not None:
        if dynamic_range <= 15:  # 理想的动态范围
            scores['dynamic'] = 100
        elif dynamic_range <= 25:  # 可接受的动态范围
            scores['dynamic'] = 80
        elif dynamic_range <= 40:  # 较大的动态范围
            scores['dynamic'] = 50
            reasons.append(f"动态范围较大 ({dynamic_range:.1f}dB)。")
        else:  # 过大的动态范围
            scores['dynamic'] = 20
            reasons.append(f"动态范围过大 ({dynamic_range:.1f}dB)，音量变化剧烈。")
            issues.append("动态范围过大")
    else:
        scores['dynamic'] = 70  # 默认中等分数

    # 6. 技术质量评分 (权重: 5%) - 削波和失真
    tech_score = 100

    # 削波检测
    if clipping is not None:
        if clipping > 1.0:
            tech_score = min(tech_score, 20)
            reasons.append(f"存在严重削波失真 ({clipping:.2f}%)。")
            issues.append("削波失真")
        elif clipping > 0.1:
            tech_score = min(tech_score, 60)
            reasons.append(f"存在轻微削波失真 ({clipping:.2f}%)。")

    # 频谱失真检测
    if distortion is not None:
        if distortion > 30.0:
            tech_score = min(tech_score, 30)
            reasons.append(f"频谱失真很高 (频谱不规则度: {distortion:.1f}%)。")
            issues.append("频谱失真")
        elif distortion > 20.0:
            tech_score = min(tech_score, 50)
            reasons.append(f"频谱失真较高 (频谱不规则度: {distortion:.1f}%)。")
            issues.append("频谱失真")
        elif distortion > 10.0:
            tech_score = min(tech_score, 70)
            reasons.append(f"存在轻微频谱失真 (频谱不规则度: {distortion:.1f}%)。")

    # 背景噪声检测
    if noise_level is not None:
        if noise_level > -30:
            tech_score = min(tech_score, 40)
            reasons.append(f"背景噪声水平很高 ({noise_level:.1f}dB)。")
        elif noise_level > -40:
            tech_score = min(tech_score, 70)
            reasons.append(f"背景噪声水平较高 ({noise_level:.1f}dB)。")

    scores['technical'] = tech_score

    # 根据评估模式选择权重分配
    if evaluation_mode == 'sleep':
        # 睡眠场景：基于科学研究优化的权重分配
        # 参考: 粉噪音82%有效 vs 白噪音33%，避免可识别声音的重要性
        weights = {
            'stability': 0.30,   # 响度稳定性 (平稳背景的重要性)
            'tonality': 0.30,    # 音调性 (避免可识别声音的关键性)
            'dynamic': 0.25,     # 动态范围 (避免突发声音的重要性)
            'spectrum': 0.10,    # 频谱质量 (偏向粉噪音，但不是最关键)
            'snr': 0.03,         # 信噪比 (睡眠场景相关性较低)
            'technical': 0.02    # 技术质量 (基础要求)
        }
    elif evaluation_mode == 'focus':
        # 专注场景：强调掩蔽效果和一致性
        weights = {
            'tonality': 0.30,    # 音调性 (避免分散注意力)
            'stability': 0.25,   # 响度稳定性
            'snr': 0.20,         # 信噪比 (掩蔽效果)
            'spectrum': 0.15,    # 频谱质量
            'dynamic': 0.05,     # 动态范围
            'technical': 0.05    # 技术质量
        }
    elif evaluation_mode == 'relaxation':
        # 放松场景：平衡各项指标，强调舒适度
        weights = {
            'stability': 0.25,   # 响度稳定性
            'tonality': 0.25,    # 音调性
            'spectrum': 0.20,    # 频谱质量
            'dynamic': 0.15,     # 动态范围
            'snr': 0.10,         # 信噪比
            'technical': 0.05    # 技术质量
        }
    else:  # 'general' 或其他
        # 通用场景：基于心理声学重要性的平衡权重
        weights = {
            'stability': 0.30,   # 响度稳定性 (提高权重)
            'tonality': 0.25,    # 音调性
            'spectrum': 0.20,    # 频谱质量
            'snr': 0.15,         # 信噪比
            'dynamic': 0.05,     # 动态范围 (降低权重)
            'technical': 0.05    # 技术质量
        }

    final_score = sum(scores[key] * weights[key] for key in scores)
    final_score = max(0, min(100, final_score))  # 确保分数在0-100范围内

    # 质量等级判断
    if final_score >= 85:
        quality = "优秀 (Excellent)"
    elif final_score >= 70:
        quality = "良好 (Good)"
    elif final_score >= 50:
        quality = "一般 (Fair)"
    elif final_score >= 30:
        quality = "较差 (Poor)"
    else:
        quality = "不推荐 (Not Recommended)"

    return quality, final_score, reasons, issues

# --- 主程序 ---
def analyze_audio_file(file_path, verbose=True, evaluation_mode='general'):
    """分析单个音频文件并返回结构化结果"""
    if verbose:
        print(f"\nAnalyzing audio file: {file_path}\n" + "="*40)

    try:
        y, sr = librosa.load(file_path, sr=None, mono=True)
    except Exception as e:
        error_msg = f"Error loading file: {e}"
        if verbose:
            print(error_msg)
        return {
            'file_path': file_path,
            'error': error_msg,
            'success': False
        }

    # 执行基础分析
    slope, linearity = calculate_spectral_slope(y, sr)
    stability = calculate_loudness_stability(y, sr)  # 现在传递采样率
    tonal_ratio = check_tonal_purity(y, sr)

    # 执行Bark尺度分析
    bark_spectrum, bark_centers = calculate_bark_spectrum(y, sr)
    bark_balance = calculate_bark_spectral_balance(bark_spectrum)

    # 执行新增的高级分析
    snr = calculate_snr(y, sr)
    dynamic_range = calculate_dynamic_range(y)
    clipping = detect_clipping(y)
    distortion = detect_spectral_distortion(y, sr)
    noise_level = assess_background_noise(y, sr)

    if any(v is None for v in [slope, linearity, stability, tonal_ratio]):
        error_msg = "Analysis failed due to an error in calculations."
        if verbose:
            print(error_msg)
        return {
            'file_path': file_path,
            'error': error_msg,
            'success': False
        }

    # 分类和判断
    noise_type = classify_noise(slope)
    quality, score, reasons, issues = judge_quality(
        stability, tonal_ratio, linearity, snr, dynamic_range,
        clipping, distortion, noise_level, evaluation_mode
    )

    # 构建结果字典
    result = {
        'file_path': file_path,
        'filename': os.path.basename(file_path),
        'success': True,
        'quality_rating': quality,
        'quality_score': score,
        'noise_type': noise_type,
        'issues': issues,
        'reasons': reasons,
        'metrics': {
            'spectral_slope': slope,
            'spectral_linearity': linearity,
            'loudness_stability': stability,
            'tonal_ratio': tonal_ratio,
            'snr_db': snr,
            'dynamic_range_db': dynamic_range,
            'clipping_percentage': clipping,
            'thd_percentage': distortion,
            'noise_level_db': noise_level,
            'bark_spectral_balance': bark_balance,
            'bark_bands_count': len(bark_spectrum) if bark_spectrum is not None else 0
        },
        'duration_seconds': len(y) / sr,
        'sample_rate': sr
    }

    if verbose:
        print_analysis_report(result)

    return result

def print_analysis_report(result):
    """打印分析报告"""
    print("📋 **音频分析报告** 📋\n")
    print(f"**最终结论:**")
    print(f"  - **质量评级:** {result['quality_rating']} (得分: {result['quality_score']}/100)")
    print(f"  - **噪音类型:** {result['noise_type']}\n")

    if result['reasons']:
        print("**评估详情与建议:**")
        for reason in result['reasons']:
            print(f"  - {reason}")
    else:
        print("**评估详情与建议:**\n  - 各项指标均表现优异，非常适合作为背景白噪音。")

    print("\n" + "-"*40)
    print("📊 **详细技术参数:**")
    metrics = result['metrics']
    print(f"  - **频谱斜率 (Spectral Slope):** {metrics['spectral_slope']:.3f} (越接近-1越像粉红噪音)")
    print(f"  - **频谱线性度 (R^2):** {metrics['spectral_linearity']:.3f} (越接近1越符合纯色噪音模型)")
    print(f"  - **感知响度稳定性 (1 - CoV):** {1-metrics['loudness_stability']:.3%} (A-weighting加权，越接近100%越稳定)")
    print(f"  - **音调峰值比 (Peak-to-Median):** {metrics['tonal_ratio']:.2f} (越低越好，表示无明显音调)")

    if metrics['snr_db'] is not None:
        print(f"  - **信噪比 (SNR):** {metrics['snr_db']:.1f} dB")
    if metrics['dynamic_range_db'] is not None:
        print(f"  - **动态范围:** {metrics['dynamic_range_db']:.1f} dB")
    if metrics['clipping_percentage'] is not None:
        print(f"  - **削波检测:** {metrics['clipping_percentage']:.3f}%")
    if metrics['thd_percentage'] is not None:
        print(f"  - **频谱失真度:** {metrics['thd_percentage']:.1f}%")
    if metrics['noise_level_db'] is not None:
        print(f"  - **背景噪声水平:** {metrics['noise_level_db']:.1f} dB")
    if metrics['bark_spectral_balance'] is not None:
        print(f"  - **Bark频谱平衡度:** {metrics['bark_spectral_balance']:.3f} (基于临界频带，越接近1越平衡)")
    if metrics['bark_bands_count'] > 0:
        print(f"  - **Bark频带数量:** {metrics['bark_bands_count']} 个临界频带")

    print(f"  - **文件时长:** {result['duration_seconds']:.1f} 秒")
    print(f"  - **采样率:** {result['sample_rate']} Hz")
    print("-" * 40)
    print("\n**注意:** 本分析不包含**循环点检测**。请手动聆听以确认循环播放时是否平滑无缝。")

def analyze_directory(directory_path, output_format='txt', evaluation_mode='general'):
    """批量分析目录中的所有音频文件"""
    # 支持的音频格式
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.m4a', '*.aac', '*.ogg', '*.ogm']

    # 查找所有音频文件
    audio_files = []
    for ext in audio_extensions:
        audio_files.extend(glob.glob(os.path.join(directory_path, ext)))
        audio_files.extend(glob.glob(os.path.join(directory_path, ext.upper())))

    if not audio_files:
        print(f"在目录 '{directory_path}' 中未找到支持的音频文件。")
        print(f"支持的格式: {', '.join([ext.replace('*', '') for ext in audio_extensions])}")
        return

    print(f"找到 {len(audio_files)} 个音频文件，开始分析...")
    print("="*60)

    results = []
    failed_files = []

    for i, file_path in enumerate(audio_files, 1):
        print(f"\n[{i}/{len(audio_files)}] 正在分析: {os.path.basename(file_path)}")
        result = analyze_audio_file(file_path, verbose=False, evaluation_mode=evaluation_mode)

        if result['success']:
            results.append(result)
            print(f"✅ 完成 - 质量评级: {result['quality_rating']} ({result['quality_score']}/100)")
        else:
            failed_files.append(result)
            print(f"❌ 失败 - {result['error']}")

    print("\n" + "="*60)
    print(f"分析完成！成功: {len(results)}, 失败: {len(failed_files)}")

    # 生成报告
    if results:
        if output_format.lower() == 'csv':
            generate_csv_report(results, directory_path)
        else:
            generate_text_report(results, failed_files, directory_path)

    return results, failed_files

def generate_text_report(results, failed_files, directory_path):
    """生成文本格式的详细报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"audio_quality_report_{timestamp}.txt"

    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("🎵 音频质量分析报告 🎵\n")
        f.write("="*60 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析目录: {directory_path}\n")
        f.write(f"总文件数: {len(results) + len(failed_files)}\n")
        f.write(f"成功分析: {len(results)}\n")
        f.write(f"分析失败: {len(failed_files)}\n\n")

        # 总体统计
        if results:
            scores = [r['quality_score'] for r in results]
            f.write("📊 总体统计\n")
            f.write("-"*30 + "\n")
            f.write(f"平均质量得分: {np.mean(scores):.1f}/100\n")
            f.write(f"最高得分: {max(scores)}/100\n")
            f.write(f"最低得分: {min(scores)}/100\n")

            # 质量分布
            excellent = sum(1 for r in results if r['quality_score'] >= 85)
            good = sum(1 for r in results if 65 <= r['quality_score'] < 85)
            fair = sum(1 for r in results if 40 <= r['quality_score'] < 65)
            poor = sum(1 for r in results if r['quality_score'] < 40)

            f.write(f"\n质量分布:\n")
            f.write(f"  优秀 (85-100分): {excellent} 个文件\n")
            f.write(f"  良好 (65-84分): {good} 个文件\n")
            f.write(f"  一般 (40-64分): {fair} 个文件\n")
            f.write(f"  不推荐 (<40分): {poor} 个文件\n\n")

            # 常见问题统计
            all_issues = []
            for r in results:
                all_issues.extend(r['issues'])

            if all_issues:
                from collections import Counter
                issue_counts = Counter(all_issues)
                f.write("🚨 常见问题统计\n")
                f.write("-"*30 + "\n")
                for issue, count in issue_counts.most_common():
                    f.write(f"  {issue}: {count} 个文件\n")
                f.write("\n")

        # 详细分析结果
        f.write("📋 详细分析结果\n")
        f.write("="*60 + "\n\n")

        # 按质量得分排序
        results_sorted = sorted(results, key=lambda x: x['quality_score'], reverse=True)

        for i, result in enumerate(results_sorted, 1):
            f.write(f"{i}. {result['filename']}\n")
            f.write("-"*40 + "\n")
            f.write(f"质量评级: {result['quality_rating']} ({result['quality_score']}/100)\n")
            f.write(f"噪音类型: {result['noise_type']}\n")
            f.write(f"文件时长: {result['duration_seconds']:.1f} 秒\n")

            if result['issues']:
                f.write(f"发现问题: {', '.join(result['issues'])}\n")

            # 技术参数
            metrics = result['metrics']
            f.write(f"\n技术参数:\n")
            f.write(f"  频谱斜率: {metrics['spectral_slope']:.3f}\n")
            f.write(f"  频谱线性度: {metrics['spectral_linearity']:.3f}\n")
            f.write(f"  响度稳定性: {1-metrics['loudness_stability']:.3%}\n")
            f.write(f"  音调峰值比: {metrics['tonal_ratio']:.2f}\n")

            if metrics['snr_db'] is not None:
                f.write(f"  信噪比: {metrics['snr_db']:.1f} dB\n")
            if metrics['dynamic_range_db'] is not None:
                f.write(f"  动态范围: {metrics['dynamic_range_db']:.1f} dB\n")
            if metrics['clipping_percentage'] is not None:
                f.write(f"  削波检测: {metrics['clipping_percentage']:.3f}%\n")
            if metrics['thd_percentage'] is not None:
                f.write(f"  总谐波失真: {metrics['thd_percentage']:.2f}%\n")
            if metrics['noise_level_db'] is not None:
                f.write(f"  背景噪声: {metrics['noise_level_db']:.1f} dB\n")

            if result['reasons']:
                f.write(f"\n评估详情:\n")
                for reason in result['reasons']:
                    f.write(f"  - {reason}\n")

            f.write("\n" + "="*40 + "\n\n")

        # 失败的文件
        if failed_files:
            f.write("❌ 分析失败的文件\n")
            f.write("-"*30 + "\n")
            for failed in failed_files:
                f.write(f"- {failed['filename']}: {failed['error']}\n")
            f.write("\n")

        # 建议
        f.write("💡 建议与总结\n")
        f.write("-"*30 + "\n")

        if results:
            # 推荐文件
            excellent_files = [r for r in results if r['quality_score'] >= 85]
            if excellent_files:
                f.write("🌟 推荐使用的高质量文件:\n")
                for file in excellent_files[:5]:  # 只显示前5个
                    f.write(f"  - {file['filename']} ({file['quality_score']}/100)\n")
                f.write("\n")

            # 需要重新处理的文件
            poor_files = [r for r in results if r['quality_score'] < 40]
            if poor_files:
                f.write("⚠️ 建议重新处理的文件:\n")
                for file in poor_files:
                    f.write(f"  - {file['filename']} ({file['quality_score']}/100)\n")
                    if file['issues']:
                        f.write(f"    问题: {', '.join(file['issues'])}\n")
                f.write("\n")

        f.write("注意: 本分析不包含循环点检测。请手动聆听以确认循环播放时是否平滑无缝。\n")

    print(f"\n📄 详细报告已保存至: {report_filename}")

def generate_csv_report(results, directory_path):
    """生成CSV格式的报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"audio_quality_report_{timestamp}.csv"

    with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
        fieldnames = [
            'filename', 'quality_rating', 'quality_score', 'noise_type', 'duration_seconds',
            'spectral_slope', 'spectral_linearity', 'loudness_stability', 'tonal_ratio',
            'snr_db', 'dynamic_range_db', 'clipping_percentage', 'thd_percentage',
            'noise_level_db', 'issues', 'file_path'
        ]

        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()

        for result in results:
            row = {
                'filename': result['filename'],
                'quality_rating': result['quality_rating'],
                'quality_score': result['quality_score'],
                'noise_type': result['noise_type'],
                'duration_seconds': result['duration_seconds'],
                'spectral_slope': result['metrics']['spectral_slope'],
                'spectral_linearity': result['metrics']['spectral_linearity'],
                'loudness_stability': result['metrics']['loudness_stability'],
                'tonal_ratio': result['metrics']['tonal_ratio'],
                'snr_db': result['metrics']['snr_db'],
                'dynamic_range_db': result['metrics']['dynamic_range_db'],
                'clipping_percentage': result['metrics']['clipping_percentage'],
                'thd_percentage': result['metrics']['thd_percentage'],
                'noise_level_db': result['metrics']['noise_level_db'],
                'issues': '; '.join(result['issues']) if result['issues'] else '',
                'file_path': result['file_path']
            }
            writer.writerow(row)

    print(f"\n📊 CSV报告已保存至: {csv_filename}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze audio files to determine their suitability as white noise.")
    parser.add_argument("path", type=str, help="Path to audio file or directory")
    parser.add_argument("--format", choices=['txt', 'csv'], default='txt',
                       help="Output format for batch analysis (default: txt)")
    parser.add_argument("--mode", choices=['general', 'sleep', 'focus', 'relaxation'],
                       default='general', help="Evaluation mode (default: general)")
    args = parser.parse_args()

    if os.path.isfile(args.path):
        # 单文件分析
        analyze_audio_file(args.path, evaluation_mode=args.mode)
    elif os.path.isdir(args.path):
        # 目录批量分析
        analyze_directory(args.path, args.format, args.mode)
    else:
        print(f"错误: 路径 '{args.path}' 不存在或不是有效的文件/目录。")