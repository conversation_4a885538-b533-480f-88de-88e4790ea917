#!/usr/bin/env python3
"""
运行完整的智能睡眠音频分析
"""

from smart_sleep_audio_system import SmartSleepAudioSystem, UserGroup
from pathlib import Path
import json

def main():
    print("🧠 智能睡眠音频评估与推荐系统")
    print("=" * 60)
    
    # 初始化系统
    system = SmartSleepAudioSystem()
    
    # 获取所有音频文件
    audio_files = list(Path('noisekun').glob('*.ogm'))
    print(f"📁 找到 {len(audio_files)} 个音频文件")
    print()
    
    results = []
    
    # 分析每个文件
    for i, file_path in enumerate(audio_files, 1):
        print(f"🔍 分析文件 {i}/{len(audio_files)}: {file_path.name}")
        
        try:
            report = system.analyze_audio_file(str(file_path))
            
            # 提取关键信息
            result = {
                'filename': file_path.name,
                'sleep_score': report.sleep_suitability.overall_score,
                'noise_type': report.audio_features.noise_type.value,
                'safety': report.safety_assessment.overall_safety.value,
                'effectiveness': report.sleep_suitability.effectiveness_prediction,
                'source': report.audio_features.audio_source.value,
                'tags': report.audio_features.sound_tags,
                'spectral_slope': report.audio_features.spectral_slope,
                'dynamic_range': report.audio_features.dynamic_range_db,
                'tonal_ratio': report.audio_features.tonal_ratio,
                'scientific_evidence': report.sleep_suitability.scientific_evidence_level,
                'overall_recommendation': report.overall_recommendation
            }
            
            # 添加个性化推荐
            result['recommendations'] = {}
            for user_group, rec in report.personalized_recommendations.items():
                result['recommendations'][user_group.value] = {
                    'score': rec.suitability_score,
                    'usage': rec.usage_recommendation,
                    'benefits': rec.benefits,
                    'risks': rec.risks
                }
            
            results.append(result)
            
            print(f"   📊 睡眠适用性: {result['sleep_score']:.1f}/100")
            print(f"   🎵 噪音类型: {result['noise_type']}")
            print(f"   🛡️ 安全等级: {result['safety']}")
            print(f"   🎯 效果预测: {result['effectiveness']:.1%}")
            print()
            
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
            print()
            continue
    
    if not results:
        print("❌ 没有成功分析的文件")
        return
    
    # 按睡眠适用性得分排序
    results.sort(key=lambda x: x['sleep_score'], reverse=True)
    
    # 显示排序结果
    print("🏆 睡眠适用性排序结果")
    print("=" * 60)
    print(f"{'排名':<4} {'文件名':<20} {'得分':<8} {'类型':<8} {'安全':<8} {'推荐'}")
    print("-" * 60)
    
    for i, result in enumerate(results, 1):
        recommendation_icon = "✅" if result['sleep_score'] >= 70 else "⚠️" if result['sleep_score'] >= 50 else "❌"
        print(f"{i:<4} {result['filename']:<20} {result['sleep_score']:5.1f}/100 {result['noise_type']:<8} {result['safety']:<8} {recommendation_icon}")
    
    print()
    
    # 科学分析总结
    print("🔬 科学分析总结")
    print("=" * 60)
    
    # 按噪音类型统计
    noise_type_stats = {}
    for result in results:
        noise_type = result['noise_type']
        if noise_type not in noise_type_stats:
            noise_type_stats[noise_type] = {'count': 0, 'avg_score': 0, 'files': []}
        noise_type_stats[noise_type]['count'] += 1
        noise_type_stats[noise_type]['files'].append(result)
    
    for noise_type, stats in noise_type_stats.items():
        avg_score = sum(f['sleep_score'] for f in stats['files']) / len(stats['files'])
        stats['avg_score'] = avg_score
    
    print("📊 噪音类型效果统计:")
    for noise_type, stats in sorted(noise_type_stats.items(), key=lambda x: x[1]['avg_score'], reverse=True):
        print(f"   {noise_type}: {stats['count']} 个文件, 平均得分 {stats['avg_score']:.1f}/100")
    
    print()
    
    # 最佳推荐
    print("🌟 最佳推荐文件")
    print("-" * 30)
    
    best_files = [r for r in results if r['sleep_score'] >= 70]
    if best_files:
        for result in best_files:
            print(f"✅ {result['filename']}")
            print(f"   📊 得分: {result['sleep_score']:.1f}/100")
            print(f"   🎵 类型: {result['noise_type']}")
            print(f"   🔬 科学依据: {result['scientific_evidence']}")
            print(f"   💡 推荐: {result['overall_recommendation']}")
            print()
    else:
        print("❌ 没有达到推荐标准的文件 (≥70分)")
        print("🔄 建议寻找粉噪音或自然水声类音频")
    
    # 保存详细结果
    with open('complete_sleep_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"📄 详细分析结果已保存到: complete_sleep_analysis.json")
    
    # 生成第一个文件的详细报告作为示例
    if results:
        best_result = results[0]
        print(f"\n📋 生成 {best_result['filename']} 的详细报告...")
        
        # 重新分析以获取完整报告对象
        report = system.analyze_audio_file(f"noisekun/{best_result['filename']}")
        detailed_report = system.generate_detailed_report(report)
        
        with open(f"{best_result['filename']}_详细报告.txt", 'w', encoding='utf-8') as f:
            f.write(detailed_report)
        
        print(f"📄 详细报告已保存到: {best_result['filename']}_详细报告.txt")

if __name__ == "__main__":
    main()
