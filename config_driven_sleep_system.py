"""
配置驱动的智能睡眠音频评估系统
基于配置文件的参数化设计，支持热更新和模板化报告生成
"""

import os
import sys
import numpy as np
import librosa
from scipy import signal
from scipy.stats import linregress
import json
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum
import warnings
import logging
from pathlib import Path

warnings.filterwarnings('ignore')

# 导入配置管理器和模板引擎
from config_manager import get_config_manager, get_config
from template_engine import get_template_manager, render_template

# 导入现有的分析工具
from noise_analyzer import (
    apply_a_weighting, calculate_bark_spectrum, calculate_bark_spectral_balance,
    calculate_snr, calculate_dynamic_range, detect_clipping, 
    detect_spectral_distortion, assess_background_noise
)

# 导入原始系统的数据类型
from smart_sleep_audio_system import (
    NoiseType, AudioSource, UserGroup, SafetyLevel,
    AudioFeatures, SafetyAssessment, SleepSuitability, 
    PersonalizedRecommendation, ComprehensiveReport
)

class ConfigDrivenSleepAudioSystem:
    """配置驱动的智能睡眠音频评估系统"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置驱动的系统
        
        Args:
            config_dir: 配置文件目录
        """
        # 初始化配置管理器
        self.config_manager = get_config_manager()
        
        # 初始化模板管理器
        self.template_manager = get_template_manager()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # 加载配置
        self._load_configuration()
        
        # 初始化声音模式数据库
        self.sound_pattern_database = self._initialize_sound_patterns()
        
        self.logger.info("配置驱动的睡眠音频评估系统初始化完成")

    def _get_highest_safety_level(self, safety_levels: List[SafetyLevel]) -> SafetyLevel:
        """获取最高（最严重）的安全级别"""
        # 定义安全级别的优先级（数值越大越严重）
        priority_map = {
            SafetyLevel.SAFE: 0,
            SafetyLevel.CAUTION: 1,
            SafetyLevel.WARNING: 2,
            SafetyLevel.DANGEROUS: 3
        }

        # 找到优先级最高的安全级别
        max_priority = max(priority_map[level] for level in safety_levels)

        # 返回对应的安全级别
        for level, priority in priority_map.items():
            if priority == max_priority:
                return level

        return SafetyLevel.SAFE  # 默认返回安全
    
    def _setup_logging(self):
        """设置日志配置"""
        log_level = get_config('logging.level', 'INFO')
        log_format = get_config('logging.format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format
        )
        
        # 如果配置了文件日志
        log_file = get_config('logging.file_path')
        if log_file:
            log_dir = Path(log_file).parent
            log_dir.mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(logging.Formatter(log_format))
            self.logger.addHandler(file_handler)
    
    def _load_configuration(self):
        """加载配置参数"""
        # 加载噪音类型有效性配置
        self.noise_effectiveness = {}
        for noise_type_name, config in get_config('noise_types', {}).items():
            try:
                noise_type = getattr(NoiseType, noise_type_name.upper())
                self.noise_effectiveness[noise_type] = config.get('effectiveness', 0.5)
            except AttributeError:
                self.logger.warning(f"未知的噪音类型: {noise_type_name}")
        
        # 加载安全阈值配置
        self.safety_thresholds = {}
        for group_name, config in get_config('safety_thresholds', {}).items():
            try:
                user_group = getattr(UserGroup, group_name.upper())
                self.safety_thresholds[user_group] = config
            except AttributeError:
                self.logger.warning(f"未知的用户群体: {group_name}")
        
        # 加载绿噪音安全配置
        self.green_noise_safety = {}
        for group_name, config in get_config('green_noise_safety', {}).items():
            try:
                user_group = getattr(UserGroup, group_name.upper())
                self.green_noise_safety[user_group] = config
            except AttributeError:
                self.logger.warning(f"未知的用户群体: {group_name}")
        
        # 加载评分权重配置
        self.scoring_weights = get_config('sleep_evaluation.scoring_weights', {
            'stability_factor': 0.30,
            'tonal_factor': 0.30,
            'dynamic_factor': 0.25,
            'spectrum_factor': 0.15
        })
        
        # 加载噪音类型评分因子
        self.noise_type_factors = get_config('sleep_evaluation.noise_type_factors', {
            'pink': 1.0,
            'brown': 0.8,
            'green': 0.7,
            'deep_red': 0.6,
            'white': 0.4,
            'complex': 0.5
        })
        
        # 加载音频来源评分因子
        self.source_factors = get_config('sleep_evaluation.source_factors', {
            'natural': 1.2,
            'mechanical': 0.8,
            'synthetic': 1.0
        })
        
        # 加载质量阈值
        self.quality_thresholds = get_config('sleep_evaluation.quality_thresholds', {
            'max_dynamic_range_db': 10,
            'max_tonal_ratio': 5,
            'min_stability_percent': 95,
            'max_disruption_risk': 0.3
        })
        
        # 加载推荐阈值
        self.recommendation_thresholds = get_config('personalized_recommendations.recommendation_thresholds', {
            'excellent': 80,
            'good': 60,
            'acceptable': 40,
            'poor': 20
        })
        
        self.logger.info("配置参数加载完成")
    
    def _initialize_sound_patterns(self) -> Dict:
        """初始化声音模式数据库"""
        # 这里可以从配置文件加载，目前使用默认值
        return {
            'water_flow': {
                'keywords': ['waterfall', 'stream', 'river', 'water'],
                'spectral_characteristics': {'low_freq_rich': True, 'stable': True},
                'sleep_benefit': 0.8
            },
            'ocean_waves': {
                'keywords': ['waves', 'ocean', 'sea'],
                'spectral_characteristics': {'rhythmic': True, 'low_freq_rich': True},
                'sleep_benefit': 0.7
            },
            'rain_sounds': {
                'keywords': ['rain', 'drops'],
                'spectral_characteristics': {'white_noise_like': True, 'consistent': True},
                'sleep_benefit': 0.75
            },
            'wind_sounds': {
                'keywords': ['wind'],
                'spectral_characteristics': {'variable': True, 'natural': True},
                'sleep_benefit': 0.6
            },
            'bird_sounds': {
                'keywords': ['bird', 'chirp'],
                'spectral_characteristics': {'tonal': True, 'identifiable': True},
                'sleep_benefit': 0.2
            },
            'weather_storm': {
                'keywords': ['storm', 'thunder', 'lightning'],
                'spectral_characteristics': {'sudden_events': True, 'high_dynamic': True},
                'sleep_benefit': 0.1
            }
        }
    
    def analyze_audio_file(self, file_path: str) -> ComprehensiveReport:
        """分析音频文件并生成综合报告"""
        try:
            self.logger.info(f"开始分析音频文件: {file_path}")
            
            # 加载音频
            y, sr = librosa.load(file_path, sr=None, mono=True)
            
            # 1. 音频特征识别
            audio_features = self._extract_audio_features(y, sr, file_path)
            
            # 2. 安全评估
            safety_assessment = self._assess_safety(audio_features)
            
            # 3. 睡眠适用性评估
            sleep_suitability = self._evaluate_sleep_suitability(audio_features)
            
            # 4. 个性化推荐
            personalized_recommendations = self._generate_personalized_recommendations(
                audio_features, safety_assessment, sleep_suitability
            )
            
            # 5. 生成总体推荐
            overall_recommendation = self._generate_overall_recommendation(
                audio_features, safety_assessment, sleep_suitability
            )
            
            # 创建综合报告
            report = ComprehensiveReport(
                audio_file=file_path,
                analysis_timestamp=datetime.now().isoformat(),
                audio_features=audio_features,
                safety_assessment=safety_assessment,
                sleep_suitability=sleep_suitability,
                personalized_recommendations=personalized_recommendations,
                overall_recommendation=overall_recommendation,
                scientific_references=self._get_scientific_references()
            )
            
            self.logger.info(f"音频文件分析完成: {file_path}")
            return report
            
        except Exception as e:
            self.logger.error(f"分析音频文件失败 {file_path}: {e}")
            raise
    
    def _extract_audio_features(self, y: np.ndarray, sr: int, file_path: str) -> AudioFeatures:
        """提取音频特征"""
        # 计算基础技术参数
        spectral_slope, spectral_linearity = self._calculate_spectral_slope(y, sr)
        loudness_stability = self._calculate_loudness_stability(y, sr)
        tonal_ratio = self._calculate_tonal_ratio(y, sr)
        
        # 高级分析
        snr_db = calculate_snr(y, sr)
        dynamic_range_db = calculate_dynamic_range(y)
        clipping_percentage = detect_clipping(y)
        spectral_distortion = detect_spectral_distortion(y, sr)
        noise_level_db = assess_background_noise(y, sr)
        
        # Bark尺度分析
        bark_spectrum, _ = calculate_bark_spectrum(y, sr)
        bark_spectral_balance = calculate_bark_spectral_balance(bark_spectrum)
        bark_bands_count = len(bark_spectrum) if bark_spectrum is not None else 0

        # 噪音类型识别
        noise_type = self._classify_noise_type(spectral_slope, bark_spectrum)

        # 音频来源识别
        audio_source = self._identify_audio_source(file_path, spectral_slope, tonal_ratio)

        # 声音标签生成
        sound_tags = self._generate_sound_tags(file_path, noise_type, audio_source, tonal_ratio, dynamic_range_db)

        # 计算音频基本信息
        duration_seconds = len(y) / sr
        rms_level_db = 20 * np.log10(np.sqrt(np.mean(y**2)) + 1e-10)

        return AudioFeatures(
            spectral_slope=spectral_slope,
            spectral_linearity=spectral_linearity,
            loudness_stability=loudness_stability,
            tonal_ratio=tonal_ratio,
            snr_db=snr_db,
            dynamic_range_db=dynamic_range_db,
            clipping_percentage=clipping_percentage,
            spectral_distortion=spectral_distortion,
            noise_level_db=noise_level_db,
            bark_spectral_balance=bark_spectral_balance,
            bark_bands_count=bark_bands_count,
            duration_seconds=duration_seconds,
            sample_rate=sr,
            rms_level_db=rms_level_db,
            noise_type=noise_type,
            audio_source=audio_source,
            sound_tags=sound_tags
        )
    
    def _calculate_spectral_slope(self, y: np.ndarray, sr: int) -> Tuple[float, float]:
        """计算频谱斜率和线性度"""
        try:
            # 使用配置的分析参数
            frame_size = get_config('analysis.spectral_analysis.frame_size', 4096)
            hop_length = get_config('analysis.spectral_analysis.hop_length', 2048)
            
            # 使用Bark尺度分析
            bark_spectrum, bark_centers = calculate_bark_spectrum(y, sr)
            
            if bark_spectrum is not None and bark_centers is not None and len(bark_spectrum) >= 3:
                valid_mask = (bark_spectrum > 0) & (bark_centers > 0)
                if np.sum(valid_mask) >= 3:
                    valid_freqs = bark_centers[valid_mask]
                    valid_spectrum = bark_spectrum[valid_mask]
                    
                    log_freqs = np.log(valid_freqs)
                    log_spectrum = np.log(valid_spectrum)
                    
                    slope, _, r_value, _, _ = linregress(log_freqs, log_spectrum)
                    linearity = r_value**2
                    
                    return slope, linearity
            
            # 回退到传统方法
            stft_result = librosa.stft(y, n_fft=frame_size, hop_length=hop_length)
            power_spectrum = np.abs(stft_result)**2
            avg_power_spectrum = np.mean(power_spectrum, axis=1)
            freqs = librosa.fft_frequencies(sr=sr, n_fft=frame_size)
            
            valid_indices = np.where((freqs > 0) & (avg_power_spectrum > 0))
            if len(valid_indices[0]) < 2:
                return 0, 0
                
            log_freqs = np.log(freqs[valid_indices])
            log_power = np.log(avg_power_spectrum[valid_indices])
            
            slope, _, r_value, _, _ = linregress(log_freqs, log_power)
            linearity = r_value**2
            
            return slope, linearity
            
        except Exception:
            return 0, 0
    
    def _calculate_loudness_stability(self, y: np.ndarray, sr: int) -> float:
        """计算响度稳定性"""
        try:
            # 使用配置的参数
            frame_duration_ms = get_config('analysis.dynamic_range.frame_duration_ms', 100)
            frame_length = int(sr * frame_duration_ms / 1000)
            
            # 应用A-weighting
            y_weighted = apply_a_weighting(y, sr)
            
            # 计算RMS能量
            rms_values = []
            for i in range(0, len(y_weighted) - frame_length, frame_length):
                frame = y_weighted[i:i + frame_length]
                rms = np.sqrt(np.mean(frame**2))
                if rms > 0:
                    rms_values.append(rms)
            
            if len(rms_values) < 2:
                return 1.0
            
            rms_values = np.array(rms_values)
            stability = np.std(rms_values) / (np.mean(rms_values) + 1e-10)
            
            return min(1.0, stability)
            
        except Exception:
            return 1.0
    
    def _calculate_tonal_ratio(self, y: np.ndarray, sr: int) -> float:
        """计算音调比率"""
        try:
            # 使用配置的参数
            frame_size = get_config('analysis.spectral_analysis.frame_size', 4096)
            
            stft_result = librosa.stft(y, n_fft=frame_size)
            magnitude_spectrum = np.abs(stft_result)
            avg_magnitude = np.mean(magnitude_spectrum, axis=1)
            
            # 找到峰值
            peaks = signal.find_peaks(avg_magnitude, height=np.max(avg_magnitude) * 0.1)[0]
            
            if len(peaks) == 0:
                return 0
            
            # 计算峰值与平均值的比率
            peak_values = avg_magnitude[peaks]
            mean_value = np.mean(avg_magnitude)
            
            if mean_value == 0:
                return 0
            
            tonal_ratio = np.max(peak_values) / mean_value
            return tonal_ratio
            
        except Exception:
            return 0

    def _classify_noise_type(self, spectral_slope: float, bark_spectrum: Optional[np.ndarray]) -> NoiseType:
        """基于配置的噪音类型分类"""
        try:
            # 从配置获取噪音类型范围
            for noise_type_name, config in get_config('noise_types', {}).items():
                if 'spectral_slope_range' in config:
                    slope_range = config['spectral_slope_range']
                    if slope_range[0] <= spectral_slope <= slope_range[1]:
                        try:
                            return getattr(NoiseType, noise_type_name.upper())
                        except AttributeError:
                            continue

            # 绿噪音特殊检测
            if bark_spectrum is not None:
                green_config = get_config('noise_types.green', {})
                if 'mid_freq_range' in green_config:
                    mid_freq_range = green_config['mid_freq_range']
                    dominance_threshold = green_config.get('dominance_threshold', 1.5)

                    # 简化的绿噪音检测逻辑
                    if self._detect_green_noise(bark_spectrum, mid_freq_range, dominance_threshold):
                        return NoiseType.GREEN

            # 默认返回复杂噪音
            return NoiseType.COMPLEX

        except Exception:
            return NoiseType.COMPLEX

    def _detect_green_noise(self, bark_spectrum: np.ndarray, mid_freq_range: List[float], threshold: float) -> bool:
        """检测绿噪音"""
        try:
            # 简化的绿噪音检测逻辑
            # 这里应该实现更复杂的中频检测算法
            mid_band_energy = np.mean(bark_spectrum[8:16])  # 大致对应中频范围
            total_energy = np.mean(bark_spectrum)

            if total_energy > 0:
                ratio = mid_band_energy / total_energy
                return ratio > threshold

            return False
        except Exception:
            return False

    def _identify_audio_source(self, file_path: str, spectral_slope: float, tonal_ratio: float) -> AudioSource:
        """识别音频来源"""
        filename = Path(file_path).stem.lower()

        # 基于文件名的自然声音关键词
        natural_keywords = ['water', 'rain', 'ocean', 'wind', 'forest', 'bird', 'nature', 'stream', 'wave']
        mechanical_keywords = ['machine', 'fan', 'engine', 'motor', 'generator']
        synthetic_keywords = ['noise', 'tone', 'generated', 'synthetic']

        if any(keyword in filename for keyword in natural_keywords):
            return AudioSource.NATURAL
        elif any(keyword in filename for keyword in mechanical_keywords):
            return AudioSource.MECHANICAL
        elif any(keyword in filename for keyword in synthetic_keywords):
            return AudioSource.SYNTHETIC

        # 基于音频特征的推断
        if tonal_ratio > 10:  # 高音调性通常是机械或合成
            return AudioSource.MECHANICAL if spectral_slope < -1 else AudioSource.SYNTHETIC

        return AudioSource.NATURAL  # 默认假设为自然声音

    def _generate_sound_tags(self, file_path: str, noise_type: NoiseType,
                           audio_source: AudioSource, tonal_ratio: float,
                           dynamic_range_db: Optional[float]) -> List[str]:
        """生成声音标签"""
        tags = []

        # 添加噪音类型标签
        tags.append(noise_type.value)

        # 添加音频来源标签
        tags.append(audio_source.value)

        # 基于文件名的标签
        filename = Path(file_path).stem.lower()
        for pattern_name, pattern_data in self.sound_pattern_database.items():
            if any(keyword in filename for keyword in pattern_data['keywords']):
                tags.append(pattern_name.replace('_', ' ').title())

        # 基于特征的标签
        if tonal_ratio > 100:
            tags.append("High Tonality")
        elif tonal_ratio > 10:
            tags.append("Moderate Tonality")

        if dynamic_range_db and dynamic_range_db > 30:
            tags.append("High Dynamic Range")
        elif dynamic_range_db and dynamic_range_db > 15:
            tags.append("Moderate Dynamic Range")

        return list(set(tags))  # 去重

    def _assess_safety(self, features: AudioFeatures) -> SafetyAssessment:
        """基于配置的安全评估"""
        warnings = []

        # 估算播放音量（简化方法）
        estimated_playback_db = 60  # 默认估算值
        if features.noise_level_db:
            estimated_playback_db = max(40, min(80, features.noise_level_db + 20))

        # 音量安全评估
        volume_safety = SafetyLevel.SAFE
        if estimated_playback_db > 70:
            volume_safety = SafetyLevel.DANGEROUS
            warnings.append("估算音量过高，可能损害听力")
        elif estimated_playback_db > 65:
            volume_safety = SafetyLevel.WARNING
            warnings.append("估算音量较高，建议降低")
        elif estimated_playback_db > 60:
            volume_safety = SafetyLevel.CAUTION
            warnings.append("注意控制音量")

        # 内容安全评估
        content_safety = SafetyLevel.SAFE

        # 使用配置的质量阈值
        max_dynamic_range = self.quality_thresholds.get('max_dynamic_range_db', 10)
        max_tonal_ratio = self.quality_thresholds.get('max_tonal_ratio', 5)

        if features.dynamic_range_db and features.dynamic_range_db > max_dynamic_range * 4:
            content_safety = self._get_highest_safety_level([content_safety, SafetyLevel.CAUTION])
            warnings.append("动态范围过大，可能有突发声音")

        if features.tonal_ratio > max_tonal_ratio * 20:
            content_safety = self._get_highest_safety_level([content_safety, SafetyLevel.CAUTION])
            warnings.append("包含明显音调成分，可能影响睡眠")

        # 时长安全评估
        duration_safety = SafetyLevel.SAFE

        # 总体安全评估（使用优先级比较）
        safety_levels = [volume_safety, content_safety, duration_safety]
        overall_safety = self._get_highest_safety_level(safety_levels)

        # 计算安全得分
        safety_score = 100
        if volume_safety == SafetyLevel.DANGEROUS:
            safety_score -= 50
        elif volume_safety == SafetyLevel.WARNING:
            safety_score -= 30
        elif volume_safety == SafetyLevel.CAUTION:
            safety_score -= 15

        if content_safety == SafetyLevel.WARNING:
            safety_score -= 20
        elif content_safety == SafetyLevel.CAUTION:
            safety_score -= 10

        # 使用配置的推荐值
        recommended_volume_range = get_config('safety_thresholds.adult.recommended_volume_range', [45, 60])
        recommended_distance = get_config('safety_thresholds.adult.min_distance_cm', 50)
        max_duration = get_config('safety_thresholds.adult.max_duration_hours', 8.0)

        return SafetyAssessment(
            overall_safety=overall_safety,
            volume_safety=volume_safety,
            duration_safety=duration_safety,
            content_safety=content_safety,
            recommended_volume_db=tuple(recommended_volume_range),
            recommended_distance_cm=recommended_distance,
            max_duration_hours=max_duration,
            warnings=warnings,
            safety_score=max(0, safety_score)
        )

    def _evaluate_sleep_suitability(self, features: AudioFeatures) -> SleepSuitability:
        """基于配置的睡眠适用性评估"""
        # 基于科学研究的效果预测
        noise_effectiveness = self.noise_effectiveness.get(features.noise_type, 0.3)

        # 计算各项睡眠质量因子
        stability_factor = max(0, 1 - features.loudness_stability)  # 稳定性越高越好
        tonal_factor = max(0, 1 - min(features.tonal_ratio / 100, 1))  # 音调性越低越好

        dynamic_factor = 1.0
        max_dynamic_range = self.quality_thresholds.get('max_dynamic_range_db', 10)
        if features.dynamic_range_db:
            dynamic_factor = max(0, 1 - max(0, features.dynamic_range_db - max_dynamic_range) / 30)

        # 频谱适合度（基于配置的噪音类型因子）
        spectrum_factor = self.noise_type_factors.get(features.noise_type.value.lower(), 0.5)

        # 音频来源评分因子
        source_factor = self.source_factors.get(features.audio_source.value.lower(), 1.0)

        # 综合睡眠适用性得分（使用配置的权重）
        overall_score = (
            stability_factor * self.scoring_weights.get('stability_factor', 0.3) +
            tonal_factor * self.scoring_weights.get('tonal_factor', 0.3) +
            dynamic_factor * self.scoring_weights.get('dynamic_factor', 0.25) +
            spectrum_factor * self.scoring_weights.get('spectrum_factor', 0.15)
        ) * source_factor * 100

        # 效果预测（基于科学研究）
        effectiveness_prediction = noise_effectiveness * (overall_score / 100)

        # 不同睡眠阶段的适用性
        sleep_phase_suitability = {
            'falling_asleep': overall_score * 0.9,  # 入睡阶段最重要
            'light_sleep': overall_score * 0.8,
            'deep_sleep': overall_score * 0.7,
            'rem_sleep': overall_score * 0.6
        }

        # 干扰风险评估（基于配置的阈值）
        disruption_risk = 0
        max_tonal_ratio = self.quality_thresholds.get('max_tonal_ratio', 5)
        max_dynamic_range = self.quality_thresholds.get('max_dynamic_range_db', 10)

        if features.tonal_ratio > max_tonal_ratio * 20:
            disruption_risk += 0.3
        if features.dynamic_range_db and features.dynamic_range_db > max_dynamic_range * 2:
            disruption_risk += 0.4
        if "Sudden Events" in features.sound_tags:
            disruption_risk += 0.5
        disruption_risk = min(1.0, disruption_risk)

        # 舒适度评估
        comfort_level = overall_score / 100
        if features.audio_source == AudioSource.NATURAL:
            comfort_level *= 1.1
        comfort_level = min(1.0, comfort_level)

        # 科学证据等级
        scientific_evidence_level = self._get_scientific_evidence_level(features.noise_type)

        return SleepSuitability(
            overall_score=overall_score,
            effectiveness_prediction=effectiveness_prediction,
            sleep_phase_suitability=sleep_phase_suitability,
            disruption_risk=disruption_risk,
            comfort_level=comfort_level,
            scientific_evidence_level=scientific_evidence_level
        )

    def _get_scientific_evidence_level(self, noise_type: NoiseType) -> str:
        """获取科学证据等级"""
        evidence_levels = {
            NoiseType.PINK: "强证据支持 (82%研究有效)",
            NoiseType.WHITE: "有限证据支持 (33%研究有效)",
            NoiseType.GREEN: "实验性支持 (55%预估有效，需更多研究)",
            NoiseType.BROWN: "理论支持 (低频偏好)",
            NoiseType.DEEP_RED: "理论支持 (低频偏好)",
            NoiseType.COMPLEX: "证据不足"
        }
        return evidence_levels.get(noise_type, "证据不足")

    def _generate_personalized_recommendations(self, features: AudioFeatures,
                                             safety: SafetyAssessment,
                                             suitability: SleepSuitability) -> Dict[UserGroup, PersonalizedRecommendation]:
        """生成个性化推荐"""
        recommendations = {}

        for user_group in UserGroup:
            # 获取用户群体的配置
            group_config = self.safety_thresholds.get(user_group, {})
            group_adjustments = get_config(f'personalized_recommendations.user_group_adjustments.{user_group.value.lower()}', {})

            # 生成最优设置
            optimal_settings = self._generate_optimal_settings(user_group, features, safety)

            # 生成科学依据
            scientific_rationale = self._generate_scientific_rationale(user_group, features, suitability)

            # 生成使用建议
            usage_recommendation = self._generate_usage_recommendation(user_group, features, safety)

            # 生成收益和风险
            benefits = self._generate_benefits(user_group, features, suitability)
            risks = self._generate_risks(user_group, features, safety)
            alternatives = self._generate_alternatives(user_group, features, suitability)

            recommendations[user_group] = PersonalizedRecommendation(
                user_group=user_group,
                suitability_score=suitability.overall_score,
                usage_recommendation=usage_recommendation,
                optimal_settings=optimal_settings,
                benefits=benefits,
                risks=risks,
                alternatives=alternatives,
                scientific_rationale=scientific_rationale
            )

        return recommendations

    def _generate_optimal_settings(self, user_group: UserGroup, features: AudioFeatures,
                                 safety: SafetyAssessment) -> Dict[str, Union[str, int, float]]:
        """生成最优设置"""
        # 获取基础安全阈值
        safety_thresholds = self.safety_thresholds.get(user_group, {})

        # 如果是绿噪音，使用专用安全阈值
        if features.noise_type == NoiseType.GREEN and user_group in self.green_noise_safety:
            green_safety = self.green_noise_safety[user_group]
            settings = {
                'volume_db': min(green_safety['max_db'], safety.recommended_volume_db[1]),
                'distance_cm': max(green_safety['min_distance_cm'], safety.recommended_distance_cm),
                'duration_hours': green_safety['max_duration_hours'],
                'timing': 'bedtime_routine',
                'special_notes': green_safety.get('special_considerations', [])
            }
        else:
            settings = {
                'volume_db': min(safety_thresholds.get('max_db', 60), safety.recommended_volume_db[1]),
                'distance_cm': max(safety_thresholds.get('min_distance_cm', 30), safety.recommended_distance_cm),
                'duration_hours': safety.max_duration_hours,
                'timing': 'bedtime_routine'
            }

        # 用户群体特定调整
        group_adjustments = get_config(f'personalized_recommendations.user_group_adjustments.{user_group.value.lower()}', {})

        if user_group == UserGroup.INFANT:
            if features.noise_type == NoiseType.GREEN:
                settings.update({
                    'volume_db': min(45, settings['volume_db']),
                    'distance_cm': max(200, settings['distance_cm']),
                    'duration_hours': 1.0,
                    'timing': 'sleep_onset_only',
                    'warning': '绿噪音不推荐用于婴幼儿'
                })
            else:
                settings.update({
                    'volume_db': min(50, settings['volume_db']),
                    'distance_cm': max(200, settings['distance_cm']),
                    'duration_hours': 1.0,
                    'timing': 'sleep_onset_only'
                })
        elif user_group == UserGroup.ELDERLY:
            settings.update({
                'volume_db': min(55, settings['volume_db']),
                'timing': 'full_night_if_needed'
            })

        return settings

    def _generate_scientific_rationale(self, user_group: UserGroup, features: AudioFeatures,
                                     suitability: SleepSuitability) -> str:
        """生成科学依据说明"""
        rationale_parts = []

        # 噪音类型的科学依据
        effectiveness = self.noise_effectiveness.get(features.noise_type, 0.3)
        if features.noise_type == NoiseType.PINK:
            rationale_parts.append("粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%")
        elif features.noise_type == NoiseType.WHITE:
            rationale_parts.append("白噪音仅在33%的研究中显示有效，效果有限")
        elif features.noise_type == NoiseType.GREEN:
            rationale_parts.append("绿噪音为新兴概念，中频集中特性预估55%有效率，但缺乏专门临床研究")

        # 用户群体特定的科学依据
        if user_group == UserGroup.INFANT:
            rationale_parts.append("研究显示80%的新生儿在5分钟内被白噪音哄睡，但需严格控制安全参数")
        elif user_group == UserGroup.ELDERLY:
            rationale_parts.append("粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现")
        elif user_group == UserGroup.INSOMNIA:
            rationale_parts.append("白噪音可使失眠患者入睡时间缩短38%，但仅作为辅助治疗")

        return "; ".join(rationale_parts)

    def _generate_usage_recommendation(self, user_group: UserGroup, features: AudioFeatures,
                                     safety: SafetyAssessment) -> str:
        """生成使用推荐"""
        if safety.overall_safety == SafetyLevel.DANGEROUS:
            return "不推荐使用：存在安全风险"

        if user_group == UserGroup.INFANT:
            if features.noise_type == NoiseType.GREEN:
                return "强烈不推荐：绿噪音中频集中可能影响婴幼儿听觉发育"
            else:
                return "谨慎使用：音量≤50dB，距离≥2米，仅在入睡阶段使用"
        elif user_group == UserGroup.ELDERLY:
            return "推荐使用：低频丰富的声音有助于深度睡眠"
        elif user_group == UserGroup.INSOMNIA:
            return "可以尝试：作为辅助治疗，建立睡眠仪式感"
        else:  # ADULT
            return "推荐使用：适合作为日常睡眠辅助"

    def _generate_benefits(self, user_group: UserGroup, features: AudioFeatures,
                          suitability: SleepSuitability) -> List[str]:
        """生成收益列表"""
        benefits = []

        if suitability.overall_score > 60:
            benefits.append("有助改善睡眠质量")

        if features.noise_type == NoiseType.PINK:
            benefits.append("科学证据支持，82%研究显示有效")

        if features.audio_source == AudioSource.NATURAL:
            benefits.append("自然声音，舒适度高")

        if user_group == UserGroup.INSOMNIA:
            benefits.append("可作为睡眠治疗的辅助手段")

        return benefits if benefits else ["基本的环境噪声屏蔽"]

    def _generate_risks(self, user_group: UserGroup, features: AudioFeatures,
                       safety: SafetyAssessment) -> List[str]:
        """生成风险列表"""
        risks = safety.warnings.copy()

        if user_group == UserGroup.INFANT and features.noise_type == NoiseType.GREEN:
            risks.append("中频集中可能影响听觉发育")

        if features.tonal_ratio > 100:
            risks.append("音调性较高，可能影响睡眠")

        if features.dynamic_range_db and features.dynamic_range_db > 20:
            risks.append("动态范围较大，可能有突发声音")

        return risks if risks else ["无明显风险"]

    def _generate_alternatives(self, user_group: UserGroup, features: AudioFeatures,
                              suitability: SleepSuitability) -> List[str]:
        """生成替代建议"""
        alternatives = []

        if suitability.overall_score < 40:
            alternatives.append("寻找粉噪音或自然水声类音频")

        if features.noise_type != NoiseType.PINK:
            alternatives.append("尝试粉噪音（科学证据最强）")

        if features.audio_source != AudioSource.NATURAL:
            alternatives.append("选择自然声音（如雨声、海浪声）")

        return alternatives if alternatives else ["当前音频已较为适合"]

    def _generate_usage_guidelines(self, user_group: UserGroup, features: AudioFeatures,
                                 safety: SafetyAssessment) -> List[str]:
        """生成使用指南"""
        guidelines = []

        # 基础使用建议
        guidelines.append(f"推荐音量: {safety.recommended_volume_db[0]}-{safety.recommended_volume_db[1]} dB")
        guidelines.append(f"设备距离: 至少 {safety.recommended_distance_cm} cm")
        guidelines.append(f"最大使用时长: {safety.max_duration_hours} 小时")

        # 用户群体特定建议
        if user_group == UserGroup.INFANT:
            guidelines.extend([
                "仅在入睡阶段使用",
                "严格控制音量，保护听力发育",
                "定时关闭，避免整夜播放"
            ])
        elif user_group == UserGroup.ELDERLY:
            guidelines.extend([
                "可整夜使用（如需要）",
                "选择低频丰富的声音",
                "配合睡前放松例行程序"
            ])
        elif user_group == UserGroup.INSOMNIA:
            guidelines.extend([
                "建立固定的睡眠仪式感",
                "避免过度依赖音频辅助",
                "配合其他睡眠卫生习惯"
            ])

        return guidelines

    def _calculate_recommendation_level(self, score: float) -> str:
        """计算推荐等级"""
        thresholds = self.recommendation_thresholds

        if score >= thresholds.get('excellent', 80):
            return "强烈推荐"
        elif score >= thresholds.get('good', 60):
            return "推荐"
        elif score >= thresholds.get('acceptable', 40):
            return "可以使用"
        elif score >= thresholds.get('poor', 20):
            return "谨慎使用"
        else:
            return "不推荐"

    def _generate_overall_recommendation(self, features: AudioFeatures, safety: SafetyAssessment,
                                       suitability: SleepSuitability) -> str:
        """生成总体推荐"""
        score = suitability.overall_score
        safety_level = safety.overall_safety

        if safety_level == SafetyLevel.DANGEROUS:
            return "不推荐 - 安全风险"
        elif safety_level == SafetyLevel.WARNING:
            return "谨慎使用 - 注意安全"
        else:
            return self._calculate_recommendation_level(score)

    def _get_scientific_references(self) -> List[str]:
        """获取科学参考文献"""
        return [
            "粉噪音82%有效率 vs 白噪音33%有效率 - 多项睡眠研究荟萃分析",
            "婴幼儿白噪音安全标准：音量≤50dB，距离≥2米 - 儿科睡眠医学研究",
            "老年人粉噪音深度睡眠改善 - 神经科学睡眠研究",
            "失眠患者入睡时间缩短38% - 临床睡眠医学试验",
            "绿噪音中频集中特性与自然环境音模拟 - 心理声学理论分析",
            "自然声音偏好与低频丰富特性 - 心理声学研究"
        ]

    def generate_report(self, report: ComprehensiveReport, format_type: str = "text",
                       user_group: Optional[str] = None, template_name: Optional[str] = None) -> str:
        """使用模板引擎生成报告"""
        try:
            # 准备模板上下文
            context = self._prepare_template_context(report, user_group)

            # 根据格式选择模板
            if template_name is None:
                template_name = self._get_default_template(format_type)

            # 渲染模板
            rendered_report = self.template_manager.render(template_name, context)

            self.logger.info(f"报告生成成功，格式: {format_type}")
            return rendered_report

        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            # 回退到简单文本报告
            return self._generate_simple_text_report(report)

    def _prepare_template_context(self, report: ComprehensiveReport, user_group: Optional[str] = None) -> Dict:
        """准备模板上下文"""
        # 转换报告数据为模板友好的格式
        context = {
            'analysis_time': report.analysis_timestamp,
            'system_version': get_config('system.version', '2.0'),
            'total_files': 1,
            'user_group': user_group,
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'analysis_results': [{
                'filename': Path(report.audio_file).name,
                'sleep_score': round(report.sleep_suitability.overall_score, 1),
                'noise_type': report.audio_features.noise_type.value,
                'safety': report.safety_assessment.overall_safety.value,
                'source': report.audio_features.audio_source.value,
                'tags': report.audio_features.sound_tags,
                'spectral_slope': round(report.audio_features.spectral_slope, 3),
                'dynamic_range': report.audio_features.dynamic_range_db,
                'tonal_ratio': round(report.audio_features.tonal_ratio, 2),
                'disruption_risk': f"{report.sleep_suitability.disruption_risk:.1%}",
                'comfort_level': f"{report.sleep_suitability.comfort_level:.1%}",
                'scientific_evidence': report.sleep_suitability.scientific_evidence_level,
                'overall_recommendation': report.overall_recommendation,
                'effectiveness': report.sleep_suitability.effectiveness_prediction,
                'safety_warnings': report.safety_assessment.warnings,
                'usage_recommendations': []
            }]
        }

        # 添加用户群体特定的推荐
        if user_group and user_group in report.personalized_recommendations:
            user_group_enum = getattr(UserGroup, user_group.upper(), None)
            if user_group_enum and user_group_enum in report.personalized_recommendations:
                recommendation = report.personalized_recommendations[user_group_enum]
                context['personalized_settings'] = recommendation.optimal_settings
                context['analysis_results'][0]['usage_recommendations'] = recommendation.usage_guidelines

        return context

    def _get_default_template(self, format_type: str) -> str:
        """获取默认模板名称"""
        template_mapping = {
            'text': 'base_report.txt',
            'markdown': 'markdown_report.md',
            'json': 'json_report.json'
        }
        return template_mapping.get(format_type, 'base_report.txt')

    def _generate_simple_text_report(self, report: ComprehensiveReport) -> str:
        """生成简单的文本报告（回退方案）"""
        lines = []
        lines.append("=" * 80)
        lines.append("🧠 智能睡眠音频评估与推荐报告")
        lines.append("=" * 80)
        lines.append(f"📁 音频文件: {report.audio_file}")
        lines.append(f"⏰ 分析时间: {report.analysis_timestamp}")
        lines.append(f"🎯 总体推荐: {report.overall_recommendation}")
        lines.append("")

        # 音频特征
        lines.append("🔍 音频特征分析")
        lines.append("-" * 40)
        lines.append(f"🎵 噪音类型: {report.audio_features.noise_type.value}")
        lines.append(f"🔊 音频来源: {report.audio_features.audio_source.value}")
        lines.append(f"📈 频谱斜率: {report.audio_features.spectral_slope:.3f}")
        lines.append(f"🎼 音调比率: {report.audio_features.tonal_ratio:.2f}")
        lines.append("")

        # 睡眠适用性
        lines.append("😴 睡眠适用性评估")
        lines.append("-" * 40)
        lines.append(f"📊 总体得分: {report.sleep_suitability.overall_score:.1f}/100")
        lines.append(f"📈 效果预测: {report.sleep_suitability.effectiveness_prediction:.2f}")
        lines.append(f"⚠️ 干扰风险: {report.sleep_suitability.disruption_risk:.1%}")
        lines.append("")

        # 安全评估
        lines.append("🛡️ 安全评估")
        lines.append("-" * 40)
        lines.append(f"🔒 安全等级: {report.safety_assessment.overall_safety.value}")
        lines.append(f"📊 安全得分: {report.safety_assessment.safety_score:.1f}/100")
        if report.safety_assessment.warnings:
            lines.append("⚠️ 安全提醒:")
            for warning in report.safety_assessment.warnings:
                lines.append(f"   - {warning}")
        lines.append("")

        lines.append("=" * 80)
        return "\n".join(lines)

# 便捷函数
def create_config_driven_system() -> ConfigDrivenSleepAudioSystem:
    """创建配置驱动的睡眠音频评估系统实例"""
    return ConfigDrivenSleepAudioSystem()

def analyze_with_config(file_path: str, format_type: str = "text",
                       user_group: Optional[str] = None) -> str:
    """便捷函数：使用配置驱动系统分析音频文件"""
    system = create_config_driven_system()
    report = system.analyze_audio_file(file_path)
    return system.generate_report(report, format_type, user_group)
