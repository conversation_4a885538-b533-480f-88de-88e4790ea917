#!/usr/bin/env python3
"""
智能睡眠音频评估系统 - 快速演示
"""

from smart_sleep_audio_system import SmartSleepAudioSystem, UserGroup

def quick_demo():
    print("🧠 智能睡眠音频评估系统 - 快速演示")
    print("=" * 60)
    
    # 初始化系统
    system = SmartSleepAudioSystem()
    print("✅ 系统初始化成功")
    
    # 演示单文件分析
    test_file = "noisekun/waterfall.ogm"
    print(f"\n🔍 分析文件: {test_file}")
    
    try:
        report = system.analyze_audio_file(test_file)
        
        print("\n📊 分析结果:")
        print(f"   睡眠适用性得分: {report.sleep_suitability.overall_score:.1f}/100")
        print(f"   噪音类型: {report.audio_features.noise_type.value}")
        print(f"   音频来源: {report.audio_features.audio_source.value}")
        print(f"   安全等级: {report.safety_assessment.overall_safety.value}")
        print(f"   效果预测: {report.sleep_suitability.effectiveness_prediction:.1%}")
        
        print("\n👥 个性化推荐:")
        for user_group, rec in report.personalized_recommendations.items():
            print(f"   {user_group.value}: {rec.suitability_score:.1f}/100")
        
        print(f"\n🎯 总体推荐: {report.overall_recommendation}")
        
        # 生成简要报告
        print("\n📄 生成详细报告...")
        detailed_report = system.generate_detailed_report(report)
        
        # 保存报告
        with open("waterfall_demo_report.txt", "w", encoding="utf-8") as f:
            f.write(detailed_report)
        
        print("✅ 详细报告已保存到: waterfall_demo_report.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_demo()
    if success:
        print("\n🎉 演示成功完成！")
        print("📋 系统功能验证:")
        print("   ✅ 音频特征识别")
        print("   ✅ 睡眠适用性评估")
        print("   ✅ 个性化推荐")
        print("   ✅ 安全评估")
        print("   ✅ 报告生成")
    else:
        print("\n❌ 演示失败")
