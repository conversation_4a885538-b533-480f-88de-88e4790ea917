#!/usr/bin/env python3
"""
生成智能睡眠音频分析报告
"""

from smart_sleep_audio_system import SmartSleepAudioSystem, UserGroup
from pathlib import Path
import json

# 手动运行分析并生成报告
def generate_report():
    system = SmartSleepAudioSystem()
    
    # 分析结果（基于我们之前的测试）
    files_to_analyze = [
        'waterfall.ogm', 'underwater.ogm', 'waves.ogm', 'wind.ogm',
        'stream-water.ogm', 'storm.ogm', 'birds-tree.ogm', 'leaves.ogm'
    ]
    
    results = []
    
    for filename in files_to_analyze:
        try:
            file_path = f"noisekun/{filename}"
            report = system.analyze_audio_file(file_path)
            
            result = {
                'filename': filename,
                'sleep_score': report.sleep_suitability.overall_score,
                'noise_type': report.audio_features.noise_type.value,
                'safety': report.safety_assessment.overall_safety.value,
                'effectiveness': report.sleep_suitability.effectiveness_prediction,
                'source': report.audio_features.audio_source.value,
                'tags': report.audio_features.sound_tags,
                'spectral_slope': report.audio_features.spectral_slope,
                'dynamic_range': report.audio_features.dynamic_range_db,
                'tonal_ratio': report.audio_features.tonal_ratio,
                'scientific_evidence': report.sleep_suitability.scientific_evidence_level,
                'overall_recommendation': report.overall_recommendation,
                'disruption_risk': report.sleep_suitability.disruption_risk,
                'comfort_level': report.sleep_suitability.comfort_level
            }
            
            # 添加个性化推荐
            result['recommendations'] = {}
            for user_group, rec in report.personalized_recommendations.items():
                result['recommendations'][user_group.value] = {
                    'score': rec.suitability_score,
                    'usage': rec.usage_recommendation,
                    'benefits': rec.benefits,
                    'risks': rec.risks,
                    'alternatives': rec.alternatives,
                    'scientific_rationale': rec.scientific_rationale
                }
            
            results.append(result)
            
        except Exception as e:
            print(f"分析 {filename} 失败: {e}")
            continue
    
    # 按得分排序
    results.sort(key=lambda x: x['sleep_score'], reverse=True)
    
    # 生成报告
    report_lines = []
    report_lines.append("🧠 智能睡眠音频评估与推荐系统 - 完整分析报告")
    report_lines.append("=" * 80)
    report_lines.append(f"📊 分析文件数量: {len(results)}")
    report_lines.append("")
    
    # 汇总统计
    report_lines.append("📈 汇总统计")
    report_lines.append("-" * 40)
    
    # 噪音类型统计
    noise_type_stats = {}
    safety_stats = {}
    source_stats = {}
    
    for result in results:
        noise_type = result['noise_type']
        safety = result['safety']
        source = result['source']
        
        noise_type_stats[noise_type] = noise_type_stats.get(noise_type, [])
        noise_type_stats[noise_type].append(result['sleep_score'])
        
        safety_stats[safety] = safety_stats.get(safety, 0) + 1
        source_stats[source] = source_stats.get(source, 0) + 1
    
    report_lines.append("🎵 噪音类型效果统计:")
    for noise_type, scores in noise_type_stats.items():
        avg_score = sum(scores) / len(scores)
        report_lines.append(f"   • {noise_type}: {len(scores)} 个文件, 平均得分 {avg_score:.1f}/100")
    
    report_lines.append("🛡️ 安全等级分布:")
    for safety, count in safety_stats.items():
        report_lines.append(f"   • {safety}: {count} 个文件")
    
    report_lines.append("🔊 音频来源分布:")
    for source, count in source_stats.items():
        report_lines.append(f"   • {source}: {count} 个文件")
    
    report_lines.append("")
    
    # 排序结果
    report_lines.append("🏆 睡眠适用性排序结果")
    report_lines.append("-" * 80)
    report_lines.append(f"{'排名':<4} {'文件名':<20} {'得分':<8} {'类型':<10} {'安全':<10} {'推荐'}")
    report_lines.append("-" * 80)
    
    for i, result in enumerate(results, 1):
        if result['sleep_score'] >= 70:
            recommendation = "✅ 强烈推荐"
        elif result['sleep_score'] >= 50:
            recommendation = "⚠️ 可以使用"
        elif result['sleep_score'] >= 30:
            recommendation = "🤔 谨慎使用"
        else:
            recommendation = "❌ 不推荐"
        
        report_lines.append(f"{i:<4} {result['filename']:<20} {result['sleep_score']:5.1f}/100 {result['noise_type']:<10} {result['safety']:<10} {recommendation}")
    
    report_lines.append("")
    
    # 科学验证结果
    report_lines.append("🔬 科学验证结果")
    report_lines.append("-" * 40)
    
    # 验证科学报告的发现
    pink_noise_files = [r for r in results if r['noise_type'] == '粉噪音']
    white_noise_files = [r for r in results if r['noise_type'] == '白噪音']
    natural_sound_files = [r for r in results if r['source'] == '自然声音']
    
    if pink_noise_files:
        avg_pink = sum(r['sleep_score'] for r in pink_noise_files) / len(pink_noise_files)
        report_lines.append(f"✅ 粉噪音文件平均得分: {avg_pink:.1f}/100 (科学研究: 82%有效)")
    
    if white_noise_files:
        avg_white = sum(r['sleep_score'] for r in white_noise_files) / len(white_noise_files)
        report_lines.append(f"⚠️ 白噪音文件平均得分: {avg_white:.1f}/100 (科学研究: 33%有效)")
    
    if natural_sound_files:
        avg_natural = sum(r['sleep_score'] for r in natural_sound_files) / len(natural_sound_files)
        report_lines.append(f"🌿 自然声音文件平均得分: {avg_natural:.1f}/100 (科学偏好)")
    
    report_lines.append("")
    
    # 最佳推荐
    report_lines.append("🌟 最佳推荐文件详细分析")
    report_lines.append("-" * 40)
    
    best_files = [r for r in results if r['sleep_score'] >= 60]  # 降低标准以显示更多结果
    
    if best_files:
        for result in best_files[:3]:  # 显示前3个
            report_lines.append(f"📁 {result['filename']}")
            report_lines.append(f"   📊 睡眠适用性得分: {result['sleep_score']:.1f}/100")
            report_lines.append(f"   🎵 噪音类型: {result['noise_type']}")
            report_lines.append(f"   🔊 音频来源: {result['source']}")
            report_lines.append(f"   🏷️  声音标签: {', '.join(result['tags'])}")
            report_lines.append(f"   📈 频谱斜率: {result['spectral_slope']:.3f}")
            report_lines.append(f"   📏 动态范围: {result['dynamic_range']:.1f} dB" if result['dynamic_range'] else "   📏 动态范围: 未检测")
            report_lines.append(f"   🎼 音调峰值比: {result['tonal_ratio']:.2f}")
            report_lines.append(f"   ⚠️  干扰风险: {result['disruption_risk']:.1%}")
            report_lines.append(f"   😌 舒适度: {result['comfort_level']:.1%}")
            report_lines.append(f"   🔬 科学证据: {result['scientific_evidence']}")
            report_lines.append(f"   🎯 总体推荐: {result['overall_recommendation']}")
            report_lines.append("")
    else:
        report_lines.append("❌ 没有达到推荐标准的文件")
    
    # 个性化推荐汇总
    report_lines.append("👥 个性化推荐汇总")
    report_lines.append("-" * 40)
    
    user_groups = ['成人', '婴幼儿', '老年人', '失眠患者']
    
    for user_group in user_groups:
        report_lines.append(f"👤 {user_group}群体:")
        
        # 找到该群体的最佳推荐
        best_for_group = None
        best_score = 0
        
        for result in results:
            if user_group in result['recommendations']:
                score = result['recommendations'][user_group]['score']
                if score > best_score:
                    best_score = score
                    best_for_group = result
        
        if best_for_group:
            rec = best_for_group['recommendations'][user_group]
            report_lines.append(f"   🏆 最佳选择: {best_for_group['filename']} (得分: {best_score:.1f}/100)")
            report_lines.append(f"   💡 使用建议: {rec['usage']}")
            report_lines.append(f"   ✅ 主要益处: {', '.join(rec['benefits'][:2])}")  # 只显示前2个
            report_lines.append(f"   ⚠️  注意事项: {', '.join(rec['risks'][:2])}")   # 只显示前2个
        else:
            report_lines.append("   ❌ 没有合适的推荐文件")
        
        report_lines.append("")
    
    # 技术改进建议
    report_lines.append("🔧 技术改进建议")
    report_lines.append("-" * 40)
    
    low_score_files = [r for r in results if r['sleep_score'] < 50]
    if low_score_files:
        report_lines.append("📉 得分较低的文件改进建议:")
        for result in low_score_files:
            report_lines.append(f"   • {result['filename']}:")
            
            if result['noise_type'] != '粉噪音':
                report_lines.append("     - 建议转换为粉噪音特性 (频谱斜率 -1.0)")
            
            if result['dynamic_range'] and result['dynamic_range'] > 15:
                report_lines.append("     - 需要压缩动态范围至 <10dB")
            
            if result['tonal_ratio'] > 100:
                report_lines.append("     - 需要抑制音调成分")
            
            if result['disruption_risk'] > 0.3:
                report_lines.append("     - 需要减少突发事件和干扰因素")
    
    report_lines.append("")
    
    # 科学参考文献
    report_lines.append("📚 科学参考文献")
    report_lines.append("-" * 40)
    references = [
        "1. 粉噪音82%有效率 vs 白噪音33%有效率 - 多项睡眠研究荟萃分析",
        "2. 婴幼儿白噪音安全标准：音量≤50dB，距离≥2米 - 儿科睡眠医学研究",
        "3. 老年人粉噪音深度睡眠改善 - 神经科学睡眠研究",
        "4. 失眠患者入睡时间缩短38% - 临床睡眠医学试验",
        "5. 自然声音偏好与低频丰富特性 - 心理声学研究"
    ]
    
    for ref in references:
        report_lines.append(ref)
    
    report_lines.append("")
    report_lines.append("=" * 80)
    report_lines.append("报告生成完成 - 智能睡眠音频评估与推荐系统")
    
    # 保存报告
    report_content = "\n".join(report_lines)
    
    with open("智能睡眠音频评估完整报告.txt", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    # 保存JSON数据
    with open("智能睡眠音频评估数据.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    return report_content, results

if __name__ == "__main__":
    report, data = generate_report()
    print("报告生成完成！")
    print(f"文本报告: 智能睡眠音频评估完整报告.txt")
    print(f"数据文件: 智能睡眠音频评估数据.json")
    print(f"分析文件数: {len(data)}")
