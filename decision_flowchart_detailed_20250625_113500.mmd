graph TD
    A1[需求输入] --> B1{需求类型分析}
    
    B1 -->|报告格式需求| C1{格式复杂度}
    B1 -->|分析功能需求| C2{算法复杂度}
    B1 -->|用户体验需求| C3{交互复杂度}
    B1 -->|集成需求| C4{系统复杂度}
    
    C1 -->|标准格式<br/>text/json| D1[现有工具]
    C1 -->|新格式<br/>markdown/html| D2[扩展工具]
    C1 -->|复杂模板<br/>多变量| D3[专用脚本]
    
    C2 -->|现有算法| D1
    C2 -->|参数调整| D2
    C2 -->|新算法| D3
    
    C3 -->|命令行增强| D2
    C3 -->|GUI需求| D3
    C3 -->|Web界面| D4[新系统]
    
    C4 -->|API调用| D2
    C4 -->|数据库集成| D3
    C4 -->|微服务架构| D4
    
    style D1 fill:#c8e6c9
    style D2 fill:#fff3e0
    style D3 fill:#ffcdd2
    style D4 fill:#e1bee7
