#!/usr/bin/env python3
"""
智能睡眠音频评估与推荐系统 - 演示脚本
展示系统的核心功能和分析结果
"""

from smart_sleep_audio_system import SmartSleepAudioSystem, UserGroup
from pathlib import Path

def demo_single_file_analysis():
    """演示单文件分析功能"""
    print("🔍 演示1: 单文件深度分析")
    print("=" * 50)
    
    system = SmartSleepAudioSystem()
    
    # 分析最佳文件
    test_file = "noisekun/waterfall.ogm"
    print(f"📁 分析文件: {test_file}")
    
    try:
        report = system.analyze_audio_file(test_file)
        
        print(f"\n📊 基础分析结果:")
        print(f"   睡眠适用性得分: {report.sleep_suitability.overall_score:.1f}/100")
        print(f"   噪音类型: {report.audio_features.noise_type.value}")
        print(f"   音频来源: {report.audio_features.audio_source.value}")
        print(f"   安全等级: {report.safety_assessment.overall_safety.value}")
        print(f"   效果预测: {report.sleep_suitability.effectiveness_prediction:.1%}")
        print(f"   科学证据: {report.sleep_suitability.scientific_evidence_level}")
        
        print(f"\n🔬 技术参数:")
        print(f"   频谱斜率: {report.audio_features.spectral_slope:.3f}")
        print(f"   响度稳定性: {report.audio_features.loudness_stability:.3f}")
        print(f"   音调峰值比: {report.audio_features.tonal_ratio:.2f}")
        print(f"   动态范围: {report.audio_features.dynamic_range_db:.1f} dB")
        print(f"   声音标签: {', '.join(report.audio_features.sound_tags)}")
        
        print(f"\n🎯 总体推荐: {report.overall_recommendation}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def demo_personalized_recommendations():
    """演示个性化推荐功能"""
    print("\n👥 演示2: 个性化推荐")
    print("=" * 50)
    
    system = SmartSleepAudioSystem()
    
    try:
        report = system.analyze_audio_file("noisekun/waterfall.ogm")
        
        user_groups = [
            (UserGroup.ADULT, "👨 成人"),
            (UserGroup.INFANT, "👶 婴幼儿"),
            (UserGroup.ELDERLY, "👴 老年人"),
            (UserGroup.INSOMNIA, "😴 失眠患者")
        ]
        
        for user_group, display_name in user_groups:
            if user_group in report.personalized_recommendations:
                rec = report.personalized_recommendations[user_group]
                
                print(f"\n{display_name}:")
                print(f"   📊 适用性得分: {rec.suitability_score:.1f}/100")
                print(f"   💡 使用建议: {rec.usage_recommendation}")
                print(f"   ✅ 主要益处: {', '.join(rec.benefits[:2])}")
                print(f"   ⚠️  注意事项: {', '.join(rec.risks[:2])}")
                
                # 显示最优设置
                settings = rec.optimal_settings
                print(f"   ⚙️  推荐设置:")
                print(f"      音量: {settings['volume_db']} dB")
                print(f"      距离: {settings['distance_cm']} cm")
                print(f"      时长: {settings['duration_hours']} 小时")
        
        return True
        
    except Exception as e:
        print(f"❌ 个性化推荐演示失败: {e}")
        return False

def demo_batch_comparison():
    """演示批量对比分析"""
    print("\n📊 演示3: 批量对比分析")
    print("=" * 50)
    
    system = SmartSleepAudioSystem()
    
    # 选择几个代表性文件进行对比
    test_files = [
        ("waterfall.ogm", "瀑布声"),
        ("underwater.ogm", "水下声"),
        ("storm.ogm", "雷雨声"),
        ("birds-tree.ogm", "鸟鸣声")
    ]
    
    results = []
    
    for filename, display_name in test_files:
        try:
            file_path = f"noisekun/{filename}"
            report = system.analyze_audio_file(file_path)
            
            results.append({
                'name': display_name,
                'filename': filename,
                'score': report.sleep_suitability.overall_score,
                'noise_type': report.audio_features.noise_type.value,
                'safety': report.safety_assessment.overall_safety.value,
                'effectiveness': report.sleep_suitability.effectiveness_prediction,
                'recommendation': report.overall_recommendation
            })
            
        except Exception as e:
            print(f"❌ 分析 {filename} 失败: {e}")
            continue
    
    if results:
        # 按得分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📈 对比分析结果 (共{len(results)}个文件):")
        print(f"{'排名':<4} {'名称':<12} {'得分':<10} {'类型':<8} {'安全':<8} {'效果':<8}")
        print("-" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"{i:<4} {result['name']:<12} {result['score']:5.1f}/100 {result['noise_type']:<8} {result['safety']:<8} {result['effectiveness']:6.1%}")
        
        print(f"\n🏆 最佳选择: {results[0]['name']} ({results[0]['score']:.1f}/100)")
        print(f"❌ 最差选择: {results[-1]['name']} ({results[-1]['score']:.1f}/100)")
        
        return True
    else:
        print("❌ 没有成功分析的文件")
        return False

def demo_scientific_validation():
    """演示科学验证功能"""
    print("\n🔬 演示4: 科学验证")
    print("=" * 50)
    
    system = SmartSleepAudioSystem()
    
    print("📚 系统集成的科学研究数据:")
    scientific_data = system.scientific_data
    
    print("\n🎵 噪音类型效果预测 (基于科学研究):")
    for noise_type, effectiveness in scientific_data['noise_effectiveness'].items():
        print(f"   {noise_type.value}: {effectiveness:.0%} 的研究显示有效")
    
    print("\n🛡️ 安全标准 (基于医学研究):")
    for user_group, thresholds in scientific_data['safety_thresholds'].items():
        print(f"   {user_group.value}: 最大音量 {thresholds['max_db']}dB, 最小距离 {thresholds['min_distance_cm']}cm")
    
    print("\n📊 最优技术参数:")
    optimal_params = scientific_data['optimal_parameters']
    print(f"   最大动态范围: {optimal_params['max_dynamic_range_db']} dB")
    print(f"   最大音调比: {optimal_params['max_tonal_ratio']}")
    print(f"   最小稳定性: {optimal_params['min_stability_percent']}%")
    
    # 验证一个实际文件
    print(f"\n🧪 实际验证 - waterfall.ogm:")
    try:
        report = system.analyze_audio_file("noisekun/waterfall.ogm")
        
        # 检查是否符合科学标准
        features = report.audio_features
        
        print(f"   噪音类型: {features.noise_type.value}")
        predicted_effectiveness = scientific_data['noise_effectiveness'].get(features.noise_type, 0.3)
        actual_effectiveness = report.sleep_suitability.effectiveness_prediction
        print(f"   预测效果: {predicted_effectiveness:.0%} → 实际效果: {actual_effectiveness:.0%}")
        
        print(f"   动态范围: {features.dynamic_range_db:.1f} dB (标准: <{optimal_params['max_dynamic_range_db']} dB)")
        print(f"   音调比: {features.tonal_ratio:.1f} (标准: <{optimal_params['max_tonal_ratio']})")
        
        # 科学一致性检查
        consistency_score = 0
        total_checks = 0
        
        if features.dynamic_range_db <= optimal_params['max_dynamic_range_db']:
            consistency_score += 1
        total_checks += 1
        
        if features.tonal_ratio <= optimal_params['max_tonal_ratio']:
            consistency_score += 1
        total_checks += 1
        
        consistency_percentage = (consistency_score / total_checks) * 100
        print(f"   科学一致性: {consistency_percentage:.0f}% ({consistency_score}/{total_checks} 项符合)")
        
        return True
        
    except Exception as e:
        print(f"❌ 科学验证失败: {e}")
        return False

def main():
    """主演示函数"""
    print("🧠 智能睡眠音频评估与推荐系统 - 功能演示")
    print("=" * 80)
    
    # 检查必要文件
    if not Path("noisekun").exists():
        print("❌ 未找到 noisekun 目录，请确保音频文件存在")
        return
    
    # 运行演示
    demos = [
        ("单文件深度分析", demo_single_file_analysis),
        ("个性化推荐", demo_personalized_recommendations),
        ("批量对比分析", demo_batch_comparison),
        ("科学验证", demo_scientific_validation)
    ]
    
    success_count = 0
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                success_count += 1
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示出错: {e}")
        
        print()  # 空行分隔
    
    print("=" * 80)
    print(f"🎯 演示总结: {success_count}/{len(demos)} 个功能演示成功")
    
    if success_count == len(demos):
        print("🎉 所有功能演示成功！系统运行正常")
        print("\n📋 系统核心功能:")
        print("   ✅ 音频特征智能识别")
        print("   ✅ 科学睡眠适用性评估")
        print("   ✅ 个性化人群推荐")
        print("   ✅ 安全标准检测")
        print("   ✅ 科学依据验证")
        
        print("\n🚀 系统已就绪，可用于:")
        print("   • 个人睡眠音频选择指导")
        print("   • 音频内容质量评估")
        print("   • 睡眠辅助产品开发")
        print("   • 医疗睡眠干预辅助")
    else:
        print("⚠️ 部分功能演示失败，请检查系统配置")

if __name__ == "__main__":
    main()
