# 🚀 智能睡眠音频评估系统 - 架构优化执行计划

## 📋 计划元信息

**计划标题**: 智能睡眠音频评估系统架构优化执行计划  
**制定时间**: 2025年06月25日 11:40:00 (中国时间)  
**计划版本**: v1.0  
**执行周期**: 4周 (2025-06-25 至 2025-07-23)  
**项目负责人**: 系统架构师  
**参与团队**: 核心开发团队 (2-3人)  

---

## 🎯 核心建议回顾

基于深入的扩展性分析，确定四个核心优化方向：

1. **🔧 P0 - 立即修复JSON序列化bug**：解决numpy类型序列化问题
2. **🔄 P1 - 功能集成统一**：将enhanced功能合并到主工具
3. **⚙️ P2 - 配置驱动架构**：实现基于配置文件的模板管理
4. **🔌 P3 - 插件化扩展**：设计可扩展的插件系统

---

## 📊 任务优先级与时间规划

### 总体时间线

| 阶段 | 时间范围 | 主要任务 | 里程碑 |
|------|----------|----------|--------|
| **第1周** | 06-25 至 07-01 | P0任务 + P1准备 | JSON bug修复完成 |
| **第2周** | 07-02 至 07-08 | P1功能集成 | 统一工具发布 |
| **第3周** | 07-09 至 07-15 | P2配置架构 | 配置系统上线 |
| **第4周** | 07-16 至 07-23 | P3插件系统 + 测试 | 完整系统交付 |

### 详细任务分解

## 🔧 P0任务：JSON序列化bug修复 (第1周)

### P0.1 问题分析与解决方案设计
**优先级**: P0  
**工作量**: 0.5人日  
**负责人**: 后端开发工程师  
**前置依赖**: 无  

**任务描述**:
- 分析numpy类型序列化失败的根本原因
- 设计通用的类型转换解决方案
- 制定向后兼容的修复策略

**验收标准**:
- [ ] 识别所有numpy类型序列化问题点
- [ ] 设计完整的convert_numpy_types函数
- [ ] 制定测试用例覆盖方案

**风险评估**: 🟢 低风险
- 问题明确，解决方案成熟
- 不涉及核心业务逻辑变更

### P0.2 实现类型转换函数
**优先级**: P0  
**工作量**: 0.3人日  
**负责人**: 后端开发工程师  
**前置依赖**: P0.1  

**具体实现**:
```python
def convert_numpy_types(obj):
    """转换numpy类型为Python原生类型，解决JSON序列化问题"""
    if isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    return obj
```

**验收标准**:
- [ ] 函数通过所有单元测试
- [ ] 支持嵌套数据结构转换
- [ ] 性能测试通过（<10ms处理时间）

### P0.3 集成到现有工具
**优先级**: P0  
**工作量**: 0.2人日  
**负责人**: 后端开发工程师  
**前置依赖**: P0.2  

**修改文件**: `run_sleep_audio_analysis.py`
**修改内容**:
- 在JSON输出前调用convert_numpy_types
- 添加错误处理和日志记录
- 更新相关文档

**验收标准**:
- [ ] JSON格式输出正常工作
- [ ] 所有现有功能保持兼容
- [ ] 回归测试100%通过

---

## 🔄 P1任务：功能集成统一 (第2周)

### P1.1 功能差异分析
**优先级**: P1  
**工作量**: 0.5人日  
**负责人**: 系统架构师  
**前置依赖**: P0任务完成  

**任务描述**:
- 对比run_sleep_audio_analysis.py和enhanced版本
- 识别需要集成的新功能
- 设计统一的参数接口

**功能对比表**:

| 功能 | 现有工具 | Enhanced版本 | 集成策略 |
|------|----------|--------------|----------|
| Markdown输出 | ❌ | ✅ | 新增--format markdown |
| 自动时间戳命名 | ❌ | ✅ | 新增--auto-name参数 |
| 对比分析表格 | ❌ | ✅ | 新增--comparison参数 |
| 报告模板 | ❌ | ✅ | 新增--template参数 |
| 增强错误处理 | 基础 | 完善 | 改进现有逻辑 |

### P1.2 参数接口设计
**优先级**: P1  
**工作量**: 0.3人日  
**负责人**: 系统架构师  
**前置依赖**: P1.1  

**新增参数设计**:
```python
parser.add_argument('--format', 
                   choices=['text', 'json', 'markdown'], 
                   default='text',
                   help='输出格式')

parser.add_argument('--auto-name', 
                   action='store_true',
                   help='自动生成带时间戳的文件名')

parser.add_argument('--comparison', 
                   action='store_true',
                   help='生成技术参数对比表格')

parser.add_argument('--template', 
                   choices=['standard', 'research', 'clinical', 'consumer'],
                   default='standard',
                   help='报告模板类型')
```

### P1.3 功能实现与集成
**优先级**: P1  
**工作量**: 1.5人日  
**负责人**: 后端开发工程师  
**前置依赖**: P1.2  

**实现步骤**:
1. 将enhanced版本的输出生成函数移植
2. 实现新的参数处理逻辑
3. 保持向后兼容性
4. 添加功能开关和降级处理

**验收标准**:
- [ ] 所有新功能正常工作
- [ ] 现有用户无感知升级
- [ ] 性能无明显回归
- [ ] 文档更新完成

### P1.4 测试与验证
**优先级**: P1  
**工作量**: 0.7人日  
**负责人**: 测试工程师  
**前置依赖**: P1.3  

**测试范围**:
- 单元测试：新增功能模块
- 集成测试：完整工作流程
- 回归测试：现有功能兼容性
- 性能测试：处理速度和内存使用

**验收标准**:
- [ ] 测试覆盖率 ≥ 85%
- [ ] 所有测试用例通过
- [ ] 性能指标符合要求
- [ ] 用户验收测试通过

---

## ⚙️ P2任务：配置驱动架构 (第3周)

### P2.1 配置系统设计
**优先级**: P2  
**工作量**: 0.8人日  
**负责人**: 系统架构师  
**前置依赖**: P1任务完成  

**配置文件结构设计**:
```yaml
# config/report_templates.yaml
system:
  version: "2.0"
  default_template: "standard"

templates:
  standard:
    title: "智能睡眠音频评估系统 - 分析报告"
    sections: ["summary", "ranking", "details", "recommendations"]
    colors:
      primary: "#2196F3"
      success: "#4CAF50"
      warning: "#FF9800"
      danger: "#F44336"
  
  research:
    title: "智能睡眠音频评估系统 - 科研分析报告"
    sections: ["summary", "ranking", "comparison", "methodology", "details"]
    include_references: true
    
  clinical:
    title: "智能睡眠音频评估系统 - 临床评估报告"
    sections: ["summary", "ranking", "safety", "clinical_guidance", "details"]
    safety_focus: true

output_formats:
  markdown:
    extension: ".md"
    generator: "MarkdownReportGenerator"
    template_engine: "jinja2"
    
  json:
    extension: ".json"
    generator: "JSONReportGenerator"
    pretty_print: true
    
  html:
    extension: ".html"
    generator: "HTMLReportGenerator"
    css_framework: "bootstrap"
```

### P2.2 配置加载器实现
**优先级**: P2  
**工作量**: 0.5人日  
**负责人**: 后端开发工程师  
**前置依赖**: P2.1  

**实现ConfigManager类**:
```python
class ConfigManager:
    def __init__(self, config_path="config/report_templates.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def get_template(self, template_name):
        """获取报告模板配置"""
        return self.config['templates'].get(template_name)
    
    def get_output_format(self, format_name):
        """获取输出格式配置"""
        return self.config['output_formats'].get(format_name)
```

### P2.3 模板引擎集成
**优先级**: P2  
**工作量**: 1.2人日  
**负责人**: 前端开发工程师  
**前置依赖**: P2.2  

**模板系统实现**:
- 集成Jinja2模板引擎
- 创建可复用的报告模板
- 实现动态内容渲染
- 支持自定义CSS样式

**验收标准**:
- [ ] 模板系统正常工作
- [ ] 支持多种输出格式
- [ ] 模板可热更新
- [ ] 性能满足要求

---

## 🔌 P3任务：插件化扩展系统 (第4周)

### P3.1 插件架构设计
**优先级**: P3  
**工作量**: 1.0人日  
**负责人**: 系统架构师  
**前置依赖**: P2任务完成  

**插件接口设计**:
```python
class ReportGeneratorPlugin:
    """报告生成器插件基类"""
    
    def __init__(self, config):
        self.config = config
    
    def generate(self, results, template_config):
        """生成报告的核心方法"""
        raise NotImplementedError
    
    def validate_config(self, config):
        """验证配置的有效性"""
        return True
    
    def get_supported_formats(self):
        """返回支持的输出格式列表"""
        raise NotImplementedError

class AnalysisPlugin:
    """分析插件基类"""
    
    def analyze(self, audio_data, config):
        """执行分析的核心方法"""
        raise NotImplementedError
    
    def get_analysis_type(self):
        """返回分析类型"""
        raise NotImplementedError
```

### P3.2 插件管理器实现
**优先级**: P3  
**工作量**: 0.8人日  
**负责人**: 后端开发工程师  
**前置依赖**: P3.1  

**PluginManager实现**:
```python
class PluginManager:
    def __init__(self, plugin_dir="plugins"):
        self.plugin_dir = plugin_dir
        self.plugins = {}
        self.load_plugins()
    
    def load_plugins(self):
        """动态加载插件"""
        for plugin_file in Path(self.plugin_dir).glob("*.py"):
            self.load_plugin(plugin_file)
    
    def register_plugin(self, plugin_class, plugin_type):
        """注册插件"""
        if plugin_type not in self.plugins:
            self.plugins[plugin_type] = {}
        self.plugins[plugin_type][plugin_class.__name__] = plugin_class
    
    def get_plugin(self, plugin_type, plugin_name):
        """获取插件实例"""
        plugin_class = self.plugins[plugin_type][plugin_name]
        return plugin_class()
```

### P3.3 示例插件开发
**优先级**: P3  
**工作量**: 0.7人日  
**负责人**: 后端开发工程师  
**前置依赖**: P3.2  

**创建示例插件**:
- HTMLReportGenerator插件
- PDFReportGenerator插件
- CustomAnalysisPlugin插件

### P3.4 集成测试与文档
**优先级**: P3  
**工作量**: 0.5人日  
**负责人**: 测试工程师 + 技术文档工程师  
**前置依赖**: P3.3  

**交付内容**:
- 插件开发指南
- API文档
- 示例代码
- 测试用例

---

## 📈 项目时间线甘特图

```
任务                    第1周    第2周    第3周    第4周
                    25 26 27 28 29 02 03 04 05 06 09 10 11 12 13 16 17 18 19 20
P0.1 问题分析        ██
P0.2 类型转换函数      ██
P0.3 集成现有工具        ██
P1.1 功能差异分析              ██
P1.2 参数接口设计                ██
P1.3 功能实现集成                  ████
P1.4 测试验证                        ██
P2.1 配置系统设计                      ██
P2.2 配置加载器                          ██
P2.3 模板引擎集成                        ████
P3.1 插件架构设计                            ██
P3.2 插件管理器                                ██
P3.3 示例插件开发                              ██
P3.4 集成测试文档                                ██
```

---

## 🎯 里程碑与交付物

### 里程碑1: JSON Bug修复 (第1周末)
**交付物**:
- [ ] 修复后的run_sleep_audio_analysis.py
- [ ] 类型转换工具函数
- [ ] 回归测试报告
- [ ] 修复说明文档

### 里程碑2: 功能统一 (第2周末)
**交付物**:
- [ ] 统一的命令行工具
- [ ] 新功能使用文档
- [ ] 迁移指南
- [ ] 性能测试报告

### 里程碑3: 配置驱动 (第3周末)
**交付物**:
- [ ] 配置文件模板
- [ ] 配置管理系统
- [ ] 模板引擎
- [ ] 配置文档

### 里程碑4: 插件系统 (第4周末)
**交付物**:
- [ ] 插件框架
- [ ] 示例插件
- [ ] 插件开发指南
- [ ] 完整系统文档

---

## ⚠️ 风险评估与缓解措施

### 技术风险

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| **向后兼容性破坏** | 🟡 中等 | 现有用户受影响 | • 保持API兼容<br/>• 渐进式升级<br/>• 充分测试 |
| **性能回归** | 🟡 中等 | 分析速度下降 | • 性能基准测试<br/>• 代码优化<br/>• 监控关键指标 |
| **配置复杂度** | 🟢 低 | 用户学习成本 | • 提供默认配置<br/>• 详细文档<br/>• 示例模板 |
| **插件安全性** | 🟡 中等 | 系统安全风险 | • 插件沙箱<br/>• 权限控制<br/>• 代码审查 |

### 项目风险

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| **时间延期** | 🟡 中等 | 交付延迟 | • 任务优先级管理<br/>• 并行开发<br/>• 敏捷迭代 |
| **资源不足** | 🟢 低 | 功能削减 | • 分阶段交付<br/>• 核心功能优先<br/>• 外部支持 |
| **需求变更** | 🟡 中等 | 计划调整 | • 需求冻结<br/>• 变更控制<br/>• 影响评估 |

---

## 🛠️ 资源需求

### 人员技能要求

| 角色 | 技能要求 | 参与阶段 | 工作量 |
|------|----------|----------|--------|
| **系统架构师** | Python、系统设计、架构模式 | 全程 | 3.1人日 |
| **后端开发工程师** | Python、JSON、YAML、插件开发 | 全程 | 4.0人日 |
| **前端开发工程师** | HTML、CSS、Jinja2、Bootstrap | P2-P3 | 1.2人日 |
| **测试工程师** | 单元测试、集成测试、性能测试 | P1、P3 | 1.2人日 |
| **技术文档工程师** | 技术写作、API文档 | P3 | 0.5人日 |

### 技术栈要求

**核心技术**:
- Python 3.8+
- PyYAML (配置文件处理)
- Jinja2 (模板引擎)
- pytest (测试框架)

**可选技术**:
- Bootstrap (HTML报告样式)
- WeasyPrint (PDF生成)
- Sphinx (文档生成)

### 开发环境

**必需工具**:
- Git (版本控制)
- Python虚拟环境
- IDE (PyCharm/VSCode)
- 测试覆盖率工具

**推荐工具**:
- Docker (环境一致性)
- GitHub Actions (CI/CD)
- SonarQube (代码质量)

---

## 📋 验收标准总览

### 功能验收标准

1. **JSON序列化修复**:
   - [ ] 所有numpy类型正确序列化
   - [ ] 现有功能100%兼容
   - [ ] 性能无明显影响

2. **功能集成统一**:
   - [ ] 新功能正常工作
   - [ ] 参数接口设计合理
   - [ ] 用户体验提升

3. **配置驱动架构**:
   - [ ] 配置文件格式规范
   - [ ] 模板系统灵活可扩展
   - [ ] 热更新支持

4. **插件化扩展**:
   - [ ] 插件接口设计清晰
   - [ ] 插件管理器稳定
   - [ ] 示例插件可用

### 质量验收标准

- **代码质量**: 通过SonarQube检查，无严重问题
- **测试覆盖率**: ≥85%
- **性能指标**: 分析速度不低于现有版本的95%
- **文档完整性**: API文档、用户指南、开发指南齐全
- **安全性**: 通过安全扫描，无高危漏洞

---

## 🔄 迁移路径

### 现有用户迁移策略

1. **平滑升级**:
   - 保持现有命令行接口不变
   - 新功能通过可选参数提供
   - 提供迁移检查工具

2. **分阶段迁移**:
   - 第1阶段：bug修复，用户无感知
   - 第2阶段：新功能可选使用
   - 第3阶段：配置文件可选采用
   - 第4阶段：插件系统可选扩展

3. **向后兼容保证**:
   - 现有脚本无需修改
   - 输出格式保持一致
   - 性能不低于原版本

### 文档更新计划

1. **用户文档**:
   - 更新README.md
   - 新增功能使用指南
   - 提供迁移示例

2. **开发文档**:
   - API参考文档
   - 插件开发指南
   - 架构设计文档

3. **运维文档**:
   - 部署指南
   - 配置管理
   - 故障排查

---

## 💻 具体代码修改示例

### P0任务：JSON序列化修复示例

**文件**: `run_sleep_audio_analysis.py`

```python
# 在文件开头添加
import numpy as np

def convert_numpy_types(obj):
    """转换numpy类型为Python原生类型，解决JSON序列化问题"""
    if isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    return obj

# 修改JSON输出部分
def output_json_format(results, output_file=None):
    """输出JSON格式结果"""
    try:
        # 转换numpy类型
        converted_results = convert_numpy_types(results)

        json_output = json.dumps(converted_results,
                                ensure_ascii=False,
                                indent=2)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_output)
        else:
            print(json_output)

    except Exception as e:
        print(f"JSON输出错误: {e}")
        # 降级处理：输出基本信息
        fallback_output = {
            "error": "JSON序列化失败",
            "message": str(e),
            "files_analyzed": len(results) if isinstance(results, list) else 1
        }
        print(json.dumps(fallback_output, ensure_ascii=False, indent=2))
```

### P1任务：参数扩展示例

```python
# 新增参数定义
parser.add_argument(
    '--format',
    choices=['text', 'json', 'markdown'],
    default='text',
    help='输出格式 (默认: text)'
)

parser.add_argument(
    '--auto-name',
    action='store_true',
    help='自动生成带时间戳的文件名'
)

parser.add_argument(
    '--comparison',
    action='store_true',
    help='生成技术参数对比表格（适用于多文件分析）'
)

parser.add_argument(
    '--template',
    choices=['standard', 'research', 'clinical', 'consumer'],
    default='standard',
    help='报告模板类型'
)

# 输出处理逻辑
def generate_output(results, args):
    """根据参数生成相应格式的输出"""

    # 确定输出文件路径
    output_path = args.output
    if args.auto_name and not output_path:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.format == 'markdown':
            output_path = f"analysis_report_{timestamp}.md"
        elif args.format == 'json':
            output_path = f"analysis_report_{timestamp}.json"
        else:
            output_path = f"analysis_report_{timestamp}.txt"

    # 生成输出内容
    if args.format == 'json':
        output_data = generate_json_output(results, args)
    elif args.format == 'markdown':
        output_data = generate_markdown_output(results, args)
    else:
        output_data = generate_text_output(results, args)

    # 输出结果
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(output_data)
        print(f"📄 报告已保存到: {output_path}")
    else:
        print(output_data)
```

### P2任务：配置系统示例

**文件**: `config/report_templates.yaml`

```yaml
system:
  version: "2.0"
  default_template: "standard"

templates:
  standard:
    title: "智能睡眠音频评估系统 - 分析报告"
    sections:
      - "summary"
      - "ranking"
      - "details"
      - "recommendations"
    colors:
      primary: "#2196F3"
      success: "#4CAF50"
      warning: "#FF9800"
      danger: "#F44336"

  research:
    title: "智能睡眠音频评估系统 - 科研分析报告"
    sections:
      - "summary"
      - "ranking"
      - "comparison"
      - "methodology"
      - "details"
    include_references: true
    scientific_focus: true

output_formats:
  markdown:
    extension: ".md"
    generator: "MarkdownReportGenerator"
    template_file: "templates/markdown_report.j2"

  json:
    extension: ".json"
    generator: "JSONReportGenerator"
    pretty_print: true
    include_metadata: true
```

**文件**: `config_manager.py`

```python
import yaml
from pathlib import Path

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_path="config/report_templates.yaml"):
        self.config_path = Path(config_path)
        self.config = self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"配置文件未找到: {self.config_path}")
            return self.get_default_config()
        except yaml.YAMLError as e:
            print(f"配置文件格式错误: {e}")
            return self.get_default_config()

    def get_template(self, template_name):
        """获取报告模板配置"""
        templates = self.config.get('templates', {})
        return templates.get(template_name, templates.get('standard', {}))

    def get_output_format(self, format_name):
        """获取输出格式配置"""
        formats = self.config.get('output_formats', {})
        return formats.get(format_name, {})

    def get_default_config(self):
        """获取默认配置"""
        return {
            'system': {'version': '2.0', 'default_template': 'standard'},
            'templates': {
                'standard': {
                    'title': '智能睡眠音频评估系统 - 分析报告',
                    'sections': ['summary', 'ranking', 'details']
                }
            },
            'output_formats': {
                'text': {'extension': '.txt'},
                'json': {'extension': '.json'},
                'markdown': {'extension': '.md'}
            }
        }
```

### P3任务：插件系统示例

**文件**: `plugin_manager.py`

```python
import importlib
import inspect
from pathlib import Path
from abc import ABC, abstractmethod

class ReportGeneratorPlugin(ABC):
    """报告生成器插件基类"""

    @abstractmethod
    def generate(self, results, config):
        """生成报告的核心方法"""
        pass

    @abstractmethod
    def get_supported_formats(self):
        """返回支持的输出格式列表"""
        pass

    def validate_config(self, config):
        """验证配置的有效性"""
        return True

class PluginManager:
    """插件管理器"""

    def __init__(self, plugin_dir="plugins"):
        self.plugin_dir = Path(plugin_dir)
        self.plugins = {
            'report_generators': {},
            'analyzers': {}
        }
        self.load_plugins()

    def load_plugins(self):
        """动态加载插件"""
        if not self.plugin_dir.exists():
            self.plugin_dir.mkdir(parents=True)
            return

        for plugin_file in self.plugin_dir.glob("*.py"):
            if plugin_file.name.startswith('__'):
                continue
            self.load_plugin(plugin_file)

    def load_plugin(self, plugin_file):
        """加载单个插件文件"""
        try:
            spec = importlib.util.spec_from_file_location(
                plugin_file.stem, plugin_file
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 查找插件类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if issubclass(obj, ReportGeneratorPlugin) and obj != ReportGeneratorPlugin:
                    self.register_plugin(obj, 'report_generators')

        except Exception as e:
            print(f"加载插件失败 {plugin_file}: {e}")

    def register_plugin(self, plugin_class, plugin_type):
        """注册插件"""
        plugin_name = plugin_class.__name__
        self.plugins[plugin_type][plugin_name] = plugin_class
        print(f"插件已注册: {plugin_name}")

    def get_plugin(self, plugin_type, plugin_name):
        """获取插件实例"""
        if plugin_type in self.plugins and plugin_name in self.plugins[plugin_type]:
            plugin_class = self.plugins[plugin_type][plugin_name]
            return plugin_class()
        return None

    def list_plugins(self, plugin_type=None):
        """列出可用插件"""
        if plugin_type:
            return list(self.plugins.get(plugin_type, {}).keys())
        return {ptype: list(plugins.keys()) for ptype, plugins in self.plugins.items()}
```

**示例插件**: `plugins/html_report_generator.py`

```python
from plugin_manager import ReportGeneratorPlugin

class HTMLReportGenerator(ReportGeneratorPlugin):
    """HTML报告生成器插件"""

    def generate(self, results, config):
        """生成HTML格式报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{title}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ color: #2196F3; border-bottom: 2px solid #2196F3; }}
                .summary {{ background: #f5f5f5; padding: 20px; margin: 20px 0; }}
                .result {{ margin: 20px 0; padding: 15px; border-left: 4px solid #4CAF50; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{title}</h1>
                <p>生成时间: {timestamp}</p>
            </div>
            <div class="summary">
                <h2>分析概览</h2>
                <p>分析文件数: {file_count}</p>
            </div>
            {content}
        </body>
        </html>
        """

        # 生成内容
        content = ""
        for result in results:
            content += f"""
            <div class="result">
                <h3>{result.audio_file}</h3>
                <p>睡眠适用性得分: {result.sleep_suitability.overall_score:.1f}/100</p>
                <p>安全等级: {result.safety_assessment.overall_safety.value}</p>
            </div>
            """

        return html_template.format(
            title=config.get('title', '分析报告'),
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            file_count=len(results),
            content=content
        )

    def get_supported_formats(self):
        """返回支持的格式"""
        return ['html']
```

---

## 📋 执行检查清单

### 第1周检查清单 (P0任务)
- [ ] JSON序列化问题完全解决
- [ ] 所有numpy类型正确转换
- [ ] 现有功能100%兼容
- [ ] 回归测试全部通过
- [ ] 性能无明显影响

### 第2周检查清单 (P1任务)
- [ ] 新参数接口设计合理
- [ ] Markdown输出格式正确
- [ ] 自动命名功能正常
- [ ] 对比分析表格完整
- [ ] 用户体验明显提升

### 第3周检查清单 (P2任务)
- [ ] 配置文件格式规范
- [ ] 配置加载器稳定
- [ ] 模板引擎正常工作
- [ ] 热更新功能可用
- [ ] 错误处理完善

### 第4周检查清单 (P3任务)
- [ ] 插件接口设计清晰
- [ ] 插件管理器稳定
- [ ] 示例插件可用
- [ ] 插件文档完整
- [ ] 安全机制有效

---

**执行计划制定完成**，请按照优先级和时间线执行各项任务。建议每周进行进度回顾和风险评估，确保项目按计划顺利推进。
