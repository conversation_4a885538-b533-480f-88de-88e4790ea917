#!/usr/bin/env python3
"""
测试模板引擎功能
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from template_engine import TemplateManager, get_template_manager, render_template

def create_test_data():
    """创建测试数据"""
    return {
        'analysis_time': '2025-06-25 14:30:00',
        'system_version': '2.0',
        'total_files': 3,
        'user_group': 'adult',
        'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'summary_stats': {
            'avg_sleep_score': 72.5,
            'safe_files_ratio': '66.7%',
            'recommended_files': 2,
            'noise_type_distribution': {
                '粉噪音': 1,
                '白噪音': 1,
                '棕噪音': 1
            },
            'safety_distribution': {
                '安全': 2,
                '警告': 1
            }
        },
        'analysis_results': [
            {
                'filename': 'waterfall.ogm',
                'sleep_score': 85.2,
                'noise_type': '棕噪音',
                'safety': '安全',
                'source': '自然',
                'tags': ['瀑布', '水流', '自然'],
                'spectral_slope': -1.849,
                'dynamic_range': 7.4,
                'tonal_ratio': 3730.7,
                'disruption_risk': '30.0%',
                'comfort_level': '85%',
                'scientific_evidence': '高',
                'overall_recommendation': '强烈推荐',
                'effectiveness': 0.82,
                'safety_warnings': [],
                'usage_recommendations': [
                    '推荐音量: 45-55 dB',
                    '设备距离: 至少 30 cm',
                    '适合整夜使用'
                ]
            },
            {
                'filename': 'pink-noise.ogm',
                'sleep_score': 78.3,
                'noise_type': '粉噪音',
                'safety': '安全',
                'source': '合成',
                'tags': ['粉噪音', '合成'],
                'spectral_slope': -1.012,
                'dynamic_range': 5.2,
                'tonal_ratio': 1250.3,
                'disruption_risk': '15.0%',
                'comfort_level': '90%',
                'scientific_evidence': '极高',
                'overall_recommendation': '推荐',
                'effectiveness': 0.82,
                'safety_warnings': [],
                'usage_recommendations': [
                    '科学验证的最佳选择',
                    '推荐音量: 45-60 dB',
                    '适合所有用户群体'
                ]
            },
            {
                'filename': 'storm.ogm',
                'sleep_score': 35.1,
                'noise_type': '复杂噪音',
                'safety': '警告',
                'source': '自然',
                'tags': ['暴风雨', '雷声', '突发'],
                'spectral_slope': -3.573,
                'dynamic_range': 74.8,
                'tonal_ratio': 3646649.5,
                'disruption_risk': '70.0%',
                'comfort_level': '25%',
                'scientific_evidence': '低',
                'overall_recommendation': '不推荐',
                'effectiveness': 0.15,
                'safety_warnings': [
                    '动态范围过大，可能有突发声音',
                    '包含明显音调成分，可能影响睡眠'
                ],
                'usage_recommendations': [
                    '不建议用于睡眠',
                    '如使用，请降低音量至30dB以下'
                ]
            }
        ],
        'best_files': [
            {
                'filename': 'waterfall.ogm',
                'sleep_score': 85.2,
                'noise_type': '棕噪音',
                'safety': '安全',
                'recommendation_reason': '自然声音，低干扰风险，适合长时间使用',
                'optimal_settings': {
                    '推荐音量': '45-55 dB',
                    '设备距离': '30 cm',
                    '使用时长': '整夜'
                }
            },
            {
                'filename': 'pink-noise.ogm',
                'sleep_score': 78.3,
                'noise_type': '粉噪音',
                'safety': '安全',
                'recommendation_reason': '科学验证的最佳噪音类型，82%有效率',
                'optimal_settings': {
                    '推荐音量': '45-60 dB',
                    '设备距离': '30 cm',
                    '使用时长': '整夜'
                }
            }
        ],
        'poor_files': [
            {
                'filename': 'storm.ogm',
                'issues': ['动态范围过大', '包含突发声音', '音调性过高'],
                'risk_level': '高',
                'rejection_reason': '不适合睡眠使用，可能干扰睡眠质量'
            }
        ],
        'personalized_settings': {
            '推荐音量范围': '45-60 dB',
            '最小设备距离': '30 cm',
            '最大使用时长': '8 小时',
            '最佳使用时机': '睡前例行程序'
        }
    }

def test_template_engine():
    """测试模板引擎基本功能"""
    print("🧪 测试模板引擎...")
    
    try:
        # 测试模板管理器初始化
        print("1. 测试模板管理器初始化...")
        template_manager = TemplateManager()
        print(f"   使用模板引擎: {template_manager.engine_type}")
        print("   ✅ 模板管理器初始化成功")
        
        # 测试模板列表
        print("2. 测试模板列表...")
        templates = template_manager.list_templates()
        print(f"   发现模板: {len(templates)} 个")
        for template in templates:
            print(f"     - {template}")
        print("   ✅ 模板列表获取成功")
        
        # 创建测试数据
        test_data = create_test_data()
        
        # 测试文本模板渲染
        print("3. 测试文本模板渲染...")
        if template_manager.template_exists('base_report.txt'):
            text_report = template_manager.render('base_report.txt', test_data)
            print(f"   文本报告长度: {len(text_report)} 字符")
            print("   ✅ 文本模板渲染成功")
        else:
            print("   ⚠️ 基础文本模板不存在")
        
        # 测试JSON模板渲染
        print("4. 测试JSON模板渲染...")
        if template_manager.template_exists('json_report.json'):
            json_report = template_manager.render('json_report.json', test_data)
            print(f"   JSON报告长度: {len(json_report)} 字符")
            # 验证JSON格式
            import json
            try:
                json.loads(json_report)
                print("   ✅ JSON模板渲染成功，格式有效")
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON格式无效: {e}")
        else:
            print("   ⚠️ JSON模板不存在")
        
        # 测试Markdown模板渲染
        print("5. 测试Markdown模板渲染...")
        if template_manager.template_exists('markdown_report.md'):
            md_report = template_manager.render('markdown_report.md', test_data)
            print(f"   Markdown报告长度: {len(md_report)} 字符")
            print("   ✅ Markdown模板渲染成功")
        else:
            print("   ⚠️ Markdown模板不存在")
        
        # 测试全局模板管理器
        print("6. 测试全局模板管理器...")
        global_report = render_template('base_report.txt', test_data)
        print(f"   全局渲染报告长度: {len(global_report)} 字符")
        print("   ✅ 全局模板管理器测试通过")
        
        print("\n✅ 所有测试通过！模板引擎工作正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_components():
    """测试模板组件功能"""
    print("\n🧪 测试模板组件...")
    
    try:
        template_manager = TemplateManager()
        test_data = create_test_data()
        
        # 测试组件模板
        components = [
            'components/file_analysis.txt',
            'components/user_group_recommendations.txt',
            'components/top_recommendations.txt',
            'components/scientific_references.txt'
        ]
        
        for component in components:
            if template_manager.template_exists(component):
                print(f"   测试组件: {component}")
                # 为组件创建适当的上下文
                if 'file_analysis' in component:
                    context = {'result': test_data['analysis_results'][0]}
                elif 'user_group' in component:
                    context = {
                        'user_group': test_data['user_group'],
                        'personalized_settings': test_data['personalized_settings']
                    }
                elif 'top_recommendations' in component:
                    context = {
                        'best_files': test_data['best_files'],
                        'poor_files': test_data['poor_files']
                    }
                else:
                    context = test_data
                
                rendered = template_manager.render(component, context)
                print(f"     渲染长度: {len(rendered)} 字符")
                print("     ✅ 组件渲染成功")
            else:
                print(f"   ⚠️ 组件不存在: {component}")
        
        print("   ✅ 组件测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        return False

def save_test_reports():
    """保存测试报告到文件"""
    print("\n💾 保存测试报告...")
    
    try:
        template_manager = TemplateManager()
        test_data = create_test_data()
        
        # 保存文本报告
        if template_manager.template_exists('base_report.txt'):
            text_report = template_manager.render('base_report.txt', test_data)
            with open('test_text_report.txt', 'w', encoding='utf-8') as f:
                f.write(text_report)
            print("   ✅ 文本报告已保存: test_text_report.txt")
        
        # 保存JSON报告
        if template_manager.template_exists('json_report.json'):
            json_report = template_manager.render('json_report.json', test_data)
            with open('test_json_report.json', 'w', encoding='utf-8') as f:
                f.write(json_report)
            print("   ✅ JSON报告已保存: test_json_report.json")
        
        # 保存Markdown报告
        if template_manager.template_exists('markdown_report.md'):
            md_report = template_manager.render('markdown_report.md', test_data)
            with open('test_markdown_report.md', 'w', encoding='utf-8') as f:
                f.write(md_report)
            print("   ✅ Markdown报告已保存: test_markdown_report.md")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始模板引擎测试")
    print("=" * 50)
    
    # 确保模板目录存在
    template_dir = Path("templates")
    template_dir.mkdir(exist_ok=True)
    
    # 运行测试
    success = True
    success &= test_template_engine()
    success &= test_template_components()
    success &= save_test_reports()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！模板系统准备就绪。")
    else:
        print("❌ 部分测试失败，请检查模板配置。")
    
    return success

if __name__ == "__main__":
    main()
