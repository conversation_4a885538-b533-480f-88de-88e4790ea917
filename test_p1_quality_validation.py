#!/usr/bin/env python3
"""
P1任务质量验证测试
验证P1集成版本是否达到质量标准
"""

import subprocess
import json
import time
import sys
from pathlib import Path

def run_command(cmd, timeout=60):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd="/Users/<USER>/Documents/Noise"
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "命令超时"

def test_feature_completeness():
    """测试功能完整性"""
    print("🧪 测试功能完整性...")
    
    # 测试详细Markdown输出
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format markdown --detailed --auto-name"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode != 0:
        print(f"❌ 详细Markdown输出失败: {stderr}")
        return False
    
    # 提取生成的文件名
    lines = stdout.split('\n')
    report_line = [line for line in lines if "报告已保存到:" in line]
    if not report_line:
        print("❌ 无法找到生成的报告文件")
        return False
    
    filename = report_line[0].split(": ")[1]
    filepath = Path(filename)
    
    if not filepath.exists():
        print(f"❌ 报告文件不存在: {filename}")
        return False
    
    # 检查文件内容
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查必需的章节
    required_sections = [
        "## 📋 报告信息",
        "## 🏆 分析结果汇总", 
        "## 📁 详细分析结果",
        "#### 🎵 音频特征分析",
        "#### 🛡️ 安全性评估",
        "#### 👥 用户群体推荐",
        "## 🔬 科学依据与使用建议"
    ]
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    if missing_sections:
        print(f"❌ 缺少必需章节: {missing_sections}")
        return False
    
    # 检查用户群体推荐
    required_groups = ["**成人**:", "**婴幼儿**:", "**老年人**:", "**失眠患者**:"]
    missing_groups = []
    for group in required_groups:
        if group not in content:
            missing_groups.append(group)
    
    if missing_groups:
        print(f"❌ 缺少用户群体推荐: {missing_groups}")
        return False
    
    print(f"✅ 功能完整性测试通过，报告文件: {filename}")
    print(f"   报告长度: {len(content)} 字符")
    print(f"   章节数量: {content.count('##')} 个")
    
    return True

def test_comparison_functionality():
    """测试对比分析功能"""
    print("\n🧪 测试对比分析功能...")
    
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise --all --format markdown --comparison --auto-name"
    returncode, stdout, stderr = run_command(cmd, timeout=120)
    
    if returncode != 0:
        print(f"❌ 对比分析失败: {stderr}")
        return False
    
    # 提取生成的文件名
    lines = stdout.split('\n')
    report_line = [line for line in lines if "报告已保存到:" in line]
    if not report_line:
        print("❌ 无法找到对比分析报告文件")
        return False
    
    filename = report_line[0].split(": ")[1]
    filepath = Path(filename)
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查对比分析相关内容
    comparison_elements = [
        "## 📊 技术参数对比分析",
        "### ⚙️ 详细技术参数",
        "| 文件名 | 频谱斜率 | 响度稳定性 |",
        "### 🔬 科学有效性对比"
    ]
    
    missing_elements = []
    for element in comparison_elements:
        if element not in content:
            missing_elements.append(element)
    
    if missing_elements:
        print(f"❌ 对比分析缺少元素: {missing_elements}")
        return False
    
    print(f"✅ 对比分析功能测试通过，报告文件: {filename}")
    return True

def test_template_system():
    """测试模板系统"""
    print("\n🧪 测试模板系统...")
    
    templates = ['standard', 'research', 'clinical', 'consumer']
    expected_titles = {
        'standard': "# 🧠 智能睡眠音频评估系统 - 分析报告",
        'research': "# 🔬 智能睡眠音频评估系统 - 科研分析报告",
        'clinical': "# 🏥 智能睡眠音频评估系统 - 临床评估报告",
        'consumer': "# 🏠 智能睡眠音频评估系统 - 消费者指南"
    }
    
    for template in templates:
        cmd = f"python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format markdown --template {template} --auto-name"
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode != 0:
            print(f"❌ {template}模板测试失败: {stderr}")
            return False
        
        # 检查生成的文件
        lines = stdout.split('\n')
        report_line = [line for line in lines if "报告已保存到:" in line]
        if not report_line:
            print(f"❌ {template}模板没有生成文件")
            return False
        
        filename = report_line[0].split(": ")[1]
        filepath = Path(filename)
        
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        expected_title = expected_titles[template]
        if expected_title not in content:
            print(f"❌ {template}模板标题不正确")
            print(f"   期望: {expected_title}")
            return False
        
        print(f"✅ {template}模板测试通过")
    
    return True

def test_performance():
    """测试性能"""
    print("\n🧪 测试性能...")
    
    # 单文件性能测试
    start_time = time.time()
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format markdown --detailed"
    returncode, stdout, stderr = run_command(cmd)
    end_time = time.time()
    
    if returncode != 0:
        print(f"❌ 单文件性能测试失败: {stderr}")
        return False
    
    single_file_time = end_time - start_time
    
    # 批量性能测试
    start_time = time.time()
    cmd = "python3 run_sleep_audio_analysis.py Sounds/Noise --all --format markdown --detailed --comparison"
    returncode, stdout, stderr = run_command(cmd, timeout=120)
    end_time = time.time()
    
    if returncode != 0:
        print(f"❌ 批量性能测试失败: {stderr}")
        return False
    
    batch_time = end_time - start_time
    avg_time_per_file = batch_time / 3  # 假设有3个文件
    
    print(f"📊 性能测试结果:")
    print(f"   单文件分析: {single_file_time:.2f}秒")
    print(f"   批量分析: {batch_time:.2f}秒 (3文件)")
    print(f"   平均每文件: {avg_time_per_file:.2f}秒")
    
    # 性能要求
    if single_file_time > 30:
        print(f"❌ 单文件性能不达标: {single_file_time:.2f}秒 > 30秒")
        return False
    
    if avg_time_per_file > 20:
        print(f"❌ 批量性能不达标: {avg_time_per_file:.2f}秒/文件 > 20秒/文件")
        return False
    
    print("✅ 性能测试通过")
    return True

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性...")
    
    # 测试所有原有命令
    old_commands = [
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav",
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --format json",
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --detailed",
        "python3 run_sleep_audio_analysis.py Sounds/Noise/white-noise.wav --user-group adult",
        "python3 run_sleep_audio_analysis.py Sounds/Noise --all"
    ]
    
    for cmd in old_commands:
        returncode, stdout, stderr = run_command(cmd, timeout=90)
        
        if returncode != 0:
            print(f"❌ 向后兼容性失败: {cmd}")
            print(f"   错误: {stderr}")
            return False
        
        # 检查基本输出
        if "智能睡眠音频评估系统" not in stdout:
            print(f"❌ 输出格式异常: {cmd}")
            return False
    
    print("✅ 向后兼容性测试通过")
    return True

def generate_quality_report(test_results):
    """生成质量报告"""
    print("\n📊 生成质量报告...")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    report = {
        "test_summary": {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests * 100
        },
        "test_details": test_results,
        "overall_status": "PASS" if passed_tests == total_tests else "FAIL",
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 保存报告
    report_file = f"quality_report_{int(time.time())}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📄 质量报告已保存: {report_file}")
    return report

def main():
    """主测试函数"""
    print("🧠 P1任务质量验证测试套件")
    print("=" * 60)
    
    tests = [
        ("功能完整性", test_feature_completeness),
        ("对比分析功能", test_comparison_functionality),
        ("模板系统", test_template_system),
        ("性能测试", test_performance),
        ("向后兼容性", test_backward_compatibility),
    ]
    
    test_results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 运行测试: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            test_results[test_name] = result
            
            if result:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            test_results[test_name] = False
            print(f"❌ {test_name} - 异常: {e}")
    
    # 生成质量报告
    quality_report = generate_quality_report(test_results)
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {quality_report['test_summary']['passed_tests']}/{quality_report['test_summary']['total_tests']} 通过")
    print(f"🎯 成功率: {quality_report['test_summary']['success_rate']:.1f}%")
    
    if quality_report['overall_status'] == 'PASS':
        print("🎉 所有质量验证测试通过！P1集成版本质量达标")
        return 0
    else:
        print("❌ 部分质量验证测试失败，需要进一步改进")
        return 1

if __name__ == "__main__":
    sys.exit(main())
