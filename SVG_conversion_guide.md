# 📊 决策流程图SVG转换指南

## 📋 文件说明

已创建的Mermaid源文件：
- `decision_flowchart_main_20250625_113500.mmd` - 主要决策流程图
- `decision_flowchart_detailed_20250625_113500.mmd` - 详细判断条件流程图

目标SVG文件：
- `decision_flowchart_main_20250625_113500.svg` - 主要决策流程图SVG
- `decision_flowchart_detailed_20250625_113500.svg` - 详细判断条件流程图SVG

## 🔧 转换方法

### 方法1: 在线Mermaid编辑器（推荐）

1. **访问在线编辑器**: https://mermaid.live/
2. **复制Mermaid代码**: 
   - 打开 `decision_flowchart_main_20250625_113500.mmd`
   - 复制全部内容到编辑器
3. **导出SVG**:
   - 点击 "Actions" 按钮
   - 选择 "Download SVG"
   - 保存为 `decision_flowchart_main_20250625_113500.svg`
4. **重复步骤**: 对详细流程图重复相同操作

### 方法2: 本地Mermaid CLI（如果有Node.js）

```bash
# 安装Mermaid CLI
npm install -g @mermaid-js/mermaid-cli

# 转换主要流程图
mmdc -i decision_flowchart_main_20250625_113500.mmd -o decision_flowchart_main_20250625_113500.svg

# 转换详细流程图
mmdc -i decision_flowchart_detailed_20250625_113500.mmd -o decision_flowchart_detailed_20250625_113500.svg
```

### 方法3: VSCode插件

1. **安装插件**: Mermaid Preview
2. **打开.mmd文件**: 在VSCode中打开Mermaid文件
3. **预览**: Ctrl+Shift+P → "Mermaid Preview"
4. **导出**: 右键预览窗口 → "Save as SVG"

### 方法4: 其他在线工具

- **Mermaid Chart**: https://www.mermaidchart.com/
- **Draw.io**: https://app.diagrams.net/ (支持Mermaid导入)
- **GitLab/GitHub**: 在Markdown文件中预览后截图

## 📐 SVG质量设置建议

### 推荐设置
- **分辨率**: 高分辨率 (300 DPI)
- **尺寸**: 宽度 1200-1600px
- **格式**: SVG (矢量格式)
- **背景**: 透明或白色

### 颜色方案
主要流程图颜色：
- 起点: `#e3f2fd` (浅蓝色)
- 现有工具: `#e8f5e8` (浅绿色)
- 扩展工具: `#fff3e0` (浅橙色)
- 专用脚本: `#ffebee` (浅红色)
- 插件化: `#f3e5f5` (浅紫色)

详细流程图颜色：
- 现有工具: `#c8e6c9` (绿色)
- 扩展工具: `#fff3e0` (橙色)
- 专用脚本: `#ffcdd2` (红色)
- 新系统: `#e1bee7` (紫色)

## 🎯 使用场景

### 文档集成
```markdown
# 决策流程图

## 主要决策流程
![主要决策流程](./decision_flowchart_main_20250625_113500.svg)

## 详细判断条件
![详细判断条件](./decision_flowchart_detailed_20250625_113500.svg)
```

### 演示文稿
- PowerPoint: 插入 → 图片 → 选择SVG文件
- Google Slides: 插入 → 图片 → 上传SVG文件
- Keynote: 拖拽SVG文件到幻灯片

### Web页面
```html
<!-- 直接嵌入 -->
<img src="decision_flowchart_main_20250625_113500.svg" alt="决策流程图" />

<!-- 或者内联SVG -->
<object data="decision_flowchart_main_20250625_113500.svg" type="image/svg+xml"></object>
```

## 📝 质量检查清单

转换完成后请检查：
- [ ] SVG文件可以正常打开
- [ ] 所有文字清晰可读
- [ ] 颜色显示正确
- [ ] 箭头和连线完整
- [ ] 文件大小合理 (通常50-200KB)
- [ ] 在不同设备上显示正常

## 🔄 更新维护

当决策流程图需要更新时：
1. 修改对应的 `.mmd` 文件
2. 重新转换为SVG
3. 更新文档中的引用
4. 提交到版本控制系统

## 📞 技术支持

如果转换过程中遇到问题：
1. 检查Mermaid语法是否正确
2. 尝试不同的在线编辑器
3. 简化复杂的图表元素
4. 联系技术团队获取帮助

---

**注意**: 请确保生成的SVG文件保存在项目根目录中，与 `decision_flowchart_20250625_113500.md` 文档保持同级，便于版本控制和文档引用。
