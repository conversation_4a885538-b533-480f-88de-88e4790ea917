#!/usr/bin/env python3
"""
简单测试智能睡眠音频评估系统
"""

import sys
from pathlib import Path

def main():
    print("开始测试...")
    
    try:
        # 导入系统
        from smart_sleep_audio_system import SmartSleepAudioSystem
        print("✅ 系统导入成功")
        
        # 初始化
        system = SmartSleepAudioSystem()
        print("✅ 系统初始化成功")
        
        # 找到一个测试文件
        test_file = Path("noisekun/waterfall.ogm")
        if not test_file.exists():
            print("❌ 测试文件不存在")
            return False
        
        print(f"🎵 分析文件: {test_file}")
        
        # 分析文件
        report = system.analyze_audio_file(str(test_file))
        print("✅ 文件分析成功")
        
        # 显示结果
        print(f"📊 睡眠适用性得分: {report.sleep_suitability.overall_score:.1f}/100")
        print(f"🛡️ 安全等级: {report.safety_assessment.overall_safety.value}")
        print(f"🎵 噪音类型: {report.audio_features.noise_type.value}")
        print(f"🔊 音频来源: {report.audio_features.audio_source.value}")
        print(f"🏷️  声音标签: {', '.join(report.audio_features.sound_tags)}")
        
        # 生成简要报告
        detailed_report = system.generate_detailed_report(report)
        
        # 保存报告
        with open("waterfall_sleep_analysis.txt", "w", encoding="utf-8") as f:
            f.write(detailed_report)
        
        print("✅ 详细报告已保存到: waterfall_sleep_analysis.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print("测试完成" if success else "测试失败")
